package cs

import (
	"context"
	"database/sql"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteLogic {
	return &DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.CsDeleteReq) error {
	err := l.svcCtx.TransModel.Trans(l.ctx, func(ctx context.Context, tx *sql.Tx) error {
		er := l.svcCtx.EmbedCsModel.DeleteTx(l.ctx, tx, req.Id)
		if er != nil {
			l.<PERSON>gger.Error("cs Delete err" + er.Error())
			return xerr.NewErrCode(xerr.DbError)
		}

		_, er = l.svcCtx.Alvector.Delete(map[string]interface{}{
			"ids": []string{req.Id},
		})
		if er != nil {
			return xerr.NewErrCodeMsg(xerr.RequestParamError, "删除失败,请重试")
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}
