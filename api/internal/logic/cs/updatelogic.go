package cs

import (
	"context"
	"database/sql"
	"encoding/json"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/openai"
	"engine/common/xerr"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateLogic {
	return &UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.CsUpdateReq) error {
	cs, err := l.svcCtx.EmbedCsModel.FindOne(l.ctx, req.Id)
	if err != nil && err != model.ErrNotFound {
		l.Logger.Error(fmt.Sprintf("cs update EmbedCsModel.FindOne err %e", err))
		return xerr.NewErrCode(xerr.DbError)
	}
	if cs == nil {
		return xerr.NewErrCode(xerr.DataNoExistError)
	}

	err = l.svcCtx.TransModel.Trans(l.ctx, func(ctx context.Context, tx *sql.Tx) error {
		embeddings, er := l.svcCtx.ChatGpt2.CreateEmbeddings(l.ctx, openai.EmbeddingRequest{
			Input: []string{
				req.Text,
			},
			Model: openai.AdaEmbeddingV3Large,
		})
		if er != nil {
			l.Logger.Error(fmt.Sprintf("cs Embeddings err %e", er))
			return xerr.NewErrMsg(er.Error())
		}

		em := embeddings.Data[0].Embedding
		if len(em) != 3072 {
			return xerr.NewErrCodeMsg(xerr.RequestParamError, "生成embedding失败,请重试")
		}
		emByte, er := json.Marshal(em)
		if er != nil {
			l.Logger.Error(fmt.Sprintf("cs Update json.Marshal err %e", er))
			return xerr.NewErrMsg(er.Error())
		}
		er = l.svcCtx.EmbedCsModel.UpdateTx(l.ctx, tx, &model.VhTextEmbeddingCs{
			Id:         req.Id,
			Title:      req.Title,
			Content:    req.Text,
			Embeddings: string(emByte),
		})
		if er != nil {
			l.Logger.Error("cs Update err" + er.Error())
			return xerr.NewErrCode(xerr.DbError)
		}

		_, er = l.svcCtx.Alvector.Update(map[string]interface{}{
			"docs": []map[string]interface{}{{
				"id":     req.Id,
				"vector": em,
				"fields": map[string]interface{}{"title": req.Title, "content": req.Text},
			}},
		})
		if er != nil {
			return xerr.NewErrCodeMsg(xerr.RequestParamError, "保存失败,请重试")
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}
