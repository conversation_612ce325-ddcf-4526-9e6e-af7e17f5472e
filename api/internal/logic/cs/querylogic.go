package cs

import (
	"context"
	"engine/common/openai"
	"engine/common/xerr"
	"fmt"
	"github.com/spf13/cast"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type QueryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewQueryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *QueryLogic {
	return &QueryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *QueryLogic) Query(req *types.CsQueryReq) (resp *types.CsQueryResp, err error) {
	embeddings, err := l.svcCtx.ChatGpt2.CreateEmbeddings(l.ctx, openai.EmbeddingRequest{
		Input: []string{
			req.Text,
		},
		Model: openai.AdaEmbeddingV3Large,
	})
	if err != nil {
		l.Logger.Error(fmt.Sprintf("cs Query err %e", err))
		return nil, xerr.NewErrMsg(err.Error())
	}

	em := embeddings.Data[0].Embedding
	if len(em) != 3072 {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "embedding失败,请重试")
	}

	query, err := l.svcCtx.Alvector.Query(map[string]interface{}{
		"vector": em,
		"topk":   1,
		//"include_vector": true,
	})

	if err != nil {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "检索失败,请重试")
	}

	resp = new(types.CsQueryResp)
	resp.List = make([]types.QueryInfo, 0)
	for _, info := range query.Output {
		resp.List = append(resp.List, types.QueryInfo{
			Id:      info.Id,
			Title:   cast.ToString(info.Fields["title"]),
			Content: cast.ToString(info.Fields["content"]),
			Score:   info.Score,
		})
	}

	return
}
