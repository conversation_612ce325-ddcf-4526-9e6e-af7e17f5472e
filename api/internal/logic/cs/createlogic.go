package cs

import (
	"context"
	"database/sql"
	"encoding/json"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/openai"
	"engine/common/xerr"
	"fmt"
	"github.com/google/uuid"
	"github.com/zeromicro/go-zero/core/logx"
)

type CreateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateLogic {
	return &CreateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateLogic) Create(req *types.CsCreateReq) error {
	id := uuid.New().String()
	if id == "" {
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "生成id失败,请重试")
	}
	err := l.svcCtx.TransModel.Trans(l.ctx, func(ctx context.Context, tx *sql.Tx) error {
		embeddings, er := l.svcCtx.ChatGpt2.CreateEmbeddings(l.ctx, openai.EmbeddingRequest{
			Input: []string{
				req.Text,
			},
			Model: openai.AdaEmbeddingV3Large,
		})
		if er != nil {
			l.Logger.Error(fmt.Sprintf("cs CreateEmbeddings err %e", er))
			return xerr.NewErrMsg(er.Error())
		}

		em := embeddings.Data[0].Embedding
		if len(em) != 3072 {
			return xerr.NewErrCodeMsg(xerr.RequestParamError, "生成embedding失败,请重试")
		}
		emByte, er := json.Marshal(em)
		if er != nil {
			l.Logger.Error(fmt.Sprintf("cs CreateEmbeddings json.Marshal err %e", er))
			return xerr.NewErrMsg(er.Error())
		}
		_, er = l.svcCtx.EmbedCsModel.InsertTx(l.ctx, tx, &model.VhTextEmbeddingCs{
			Id:         id,
			Title:      req.Title,
			Content:    req.Text,
			Embeddings: string(emByte),
		})
		if er != nil {
			l.Logger.Error("cs Create err" + er.Error())
			return xerr.NewErrCode(xerr.DbError)
		}

		_, er = l.svcCtx.Alvector.Save(map[string]interface{}{
			"docs": []map[string]interface{}{{
				"id":     id,
				"vector": em,
				"fields": map[string]interface{}{"title": req.Title, "content": req.Text},
			}},
		})
		if er != nil {
			return xerr.NewErrCodeMsg(xerr.RequestParamError, "保存失败,请重试")
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}
