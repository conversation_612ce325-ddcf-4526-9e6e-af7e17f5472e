package logic

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/vine"
	"errors"
	"github.com/go-resty/resty/v2"
	"github.com/spf13/cast"
	"golang.org/x/sync/errgroup"
	"sync"
	"time"
)

type FineTunesInstructVine struct {
	types.FineTunesInstructInfo
	Resp       interface{} `json:"resp"`
	RespErrMsg string      `json:"resp_err_msg"`
	RespCode   int64       `json:"resp_code"`
}

func DoHttpServer(svc *svc.ServiceContext, instructs []types.FineTunesInstructInfo) (rsp []*FineTunesInstructVine) {
	cli := resty.New().SetTimeout(3 * time.Second).R()

	//todo 验证路由地址是否注册 并拿到host
	serviceUrl := "http://test.com"

	var wait errgroup.Group
	var mutex sync.Mutex

	for _, instruct := range instructs {
		wait.Go(func() error {
			resp := &FineTunesInstructVine{FineTunesInstructInfo: instruct, Resp: struct{}{}}
			response := &vine.FineTunesResponse{}

			//test
			if instruct.Path == "/wms/v3/search/stockByBarCode" || instruct.Path == "/commodities/v3/search/stockByPeriod" {
				var list []map[string]interface{}
				datas := cast.ToStringSlice(instruct.Data)
				for _, d := range datas {
					c := "barCode"
					if instruct.Path == "/commodities/v3/search/stockByPeriod" {
						c = "period"
					}
					list = append(list, map[string]interface{}{c: d, "stock": 123})
				}
				if instruct.Path == "/wms/v3/search/stockByBarCode" {
					resp.Resp = map[string]interface{}{"header": []string{"条码", "可用库存"}, "list": list}
				} else {
					resp.Resp = map[string]interface{}{"header": []string{"期数", "可用库存"}, "list": list}
				}
				rsp = append(rsp, resp)
				return nil
			}

			defer mutex.Unlock()

			_, err := cli.SetBody(map[string]interface{}{"data": instruct.Data}).SetResult(response).
				Post(serviceUrl + instruct.Path)
			if err != nil {
				if err != context.DeadlineExceeded {
					resp.RespCode = 504
					resp.RespErrMsg = "请求超时!"
				} else {
					resp.RespErrMsg = err.Error()
				}
				mutex.Lock()
				rsp = append(rsp, resp)
				return err
			}

			if response.Code != vine.CodeOk {
				resp.RespErrMsg = response.Msg
				resp.RespCode = response.Code
				mutex.Lock()
				rsp = append(rsp, resp)
				return errors.New(response.Msg)
			}

			resp.Resp = response.Data
			mutex.Lock()
			rsp = append(rsp, resp)

			return nil
		})
	}

	err := wait.Wait()
	if err != nil {

	}

	return rsp
}
