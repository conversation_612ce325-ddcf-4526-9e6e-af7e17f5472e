package completion

import (
	"context"
	"engine/api/internal/svc"
	"engine/common/xerr"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/logx"
	"io"
	"io/ioutil"
	"net/http"
)

type ForwardLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewForwardLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ForwardLogic {
	return &ForwardLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

const completionsUrl = "https://api.openai.com/v1/chat/completions"

func (l *ForwardLogic) Forward(data io.Reader) (resp interface{}, err error) {
	r, err := http.NewRequest("POST", completionsUrl, data)
	if err != nil {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "Error creating request")
	}
	r.Header.Set("Content-Type", "application/json")
	r.Header.Set("Authorization", "Bearer ********************************************************************************************************************************************************************")

	//proxy, _ := url.Parse("http://127.0.0.1:7890")
	// 执行请求
	client := &http.Client{
		/*Transport: &http.Transport{
			Proxy: http.ProxyURL(proxy),
		},*/
	}
	rsp, err := client.Do(r)
	if err != nil {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "Error sending request")
	}
	defer rsp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(rsp.Body)
	if err != nil {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "Error reading response body")
	}

	// 检查响应状态码
	/*if rsp.StatusCode < 200 || rsp.StatusCode >= 300 {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, fmt.Sprintf("Remote server error statusCode: %d", rsp.StatusCode))
	}*/

	return cast.ToStringMap(string(body)), nil
}
