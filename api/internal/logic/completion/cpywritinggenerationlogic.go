package completion

import (
	"context"
	"encoding/json"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/openai"
	"engine/common/xerr"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"
)

type CpywritingGenerationLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCpywritingGenerationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CpywritingGenerationLogic {
	return &CpywritingGenerationLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

type generator struct {
	Title           string `json:"title"`
	Subtitle        string `json:"subtitle"`
	FirstParagraph  string `json:"first_paragraph"`
	SecondParagraph string `json:"second_paragraph"`
	ThirdParagraph  string `json:"third_paragraph"`
}

func (l *CpywritingGenerationLogic) CpywritingGeneration(req *types.CpywritingGenerationReq) (resp *types.CpywritingGenerationResp, err error) {
	var message []openai.ChatCompletionMessage
	message = append(message, openai.ChatCompletionMessage{
		Role: openai.ChatMessageRoleSystem,
		//Content: "你是专业的酒行业文案写作大师，根据以下提供的［文案写作规范］和USER提供的［关键信息］来生成一篇专业商品详情文案(文案里不能出现卖价和任何售卖链接)。生成的文案内容请确保保留换行符（\\n），并最终以 JSON 格式返回. \n\n[文案写作规范］：\n\n标题：【括号内汉字不超过10个】酒款中文名+卖点信息：如直降 秒杀 低均(市场均价和卖价得到) 等等+ 酒款原文名\n\n副标题：3个最重要的卖点，不超过3句话，24个汉字。如名庄，高分，好年份，好园子, 好价格 等等。\n\n正文(1000字左右):\n第一段话：将副标题展开来写。(最少100字) \n\n第二段话：选择最突出的一个特点/卖点进行商品的介绍。(年份好就介绍年份，园子好就介绍园子，酿酒师强就介绍酿酒师，如果都没有，就找高分酒评来描述酒款。)(最少200字)\n\n第三段话：介绍酒庄及引用酒评家对酒庄的好评。(最少400字)\n\n[示例]:{\"title\":\"【Prosecco起泡巨头！Luxury奢华系列DOCG】Mionetto Luxury Valdobbiadene DOCG Extra Dry \",\"subtitle\":\"斩获多项重磅国际赛事金、银大奖，原产地DOCG卓越品质，Mionetto人气进阶酒款，轰趴、聚友、配餐轻松拿捏！\",\"text\":\"<p style=\"text-align: left;\"><strong>【Prosecco起泡巨头！Luxury奢华系列，炫酷个性黑标】Mionetto Luxury Valdobbiadene DOCG Extra Dry&nbsp;</strong></p>\n<p style=\"text-align: justify;\">&nbsp;</p>\n<p style=\"text-align: justify;\">&nbsp;</p>\n<p style=\"text-align: justify;\"><strong><span style=\"color: #ff0000;\">世界销量排名first的Prosecco &middot; 意大利超牛的国际品牌&nbsp; &middot; 百年如一日专注高品质起泡酒巨头Mionetto炫酷款上新啦！</span></strong></p>\n<p style=\"text-align: justify;\">&nbsp;</p>\n<p style=\"text-align: justify;\">&nbsp;</p>\n<p style=\"text-align: justify;\">一眼吸睛，<strong>黑白配色超酷瓶标</strong>，做人群中那个醉靓的仔；二斩大奖，<strong>多项重磅国际赛事金、银奖项傍身</strong>，品质无所畏惧；醉后，<strong>原产地DOCG法定认证</strong>，精准定位Valdobbiadene DOCG产区中的优质风土葡萄园。</p>\n<p style=\"text-align: justify;\">&nbsp;</p>\n<p style=\"text-align: justify;\">&nbsp;</p>\n<p style=\"text-align: justify;\">Mionetto于1887年创立，夺下多个&ldquo;First&rdquo;的称号：</p>\n<p style=\"text-align: justify;\">&nbsp;</p>\n<p><span style=\"font-size: 20px;\"><strong><span style=\"color: #ff6600;\">●○●</span></strong></span>意大利餐厅<em><span style=\"color: #ff6600;\">销量First品牌；全球零售额First品牌</span></em>；</p>\n<p style=\"text-align: justify;\">&nbsp;</p>\n<p style=\"text-align: justify;\"><span style=\"color: #ff6600; font-size: 20px;\"><strong>○●○</strong></span>脸书<span style=\"color: #ff6600;\">First受欢迎的Prosecco品牌</span>；</p>\n<p style=\"text-align: justify;\">&nbsp;</p>\n<p><span style=\"font-size: 20px;\"><strong><span style=\"color: #ff6600;\">●○●</span></strong></span>除此之外，它还连续11年获得<span style=\"color: #ff6600;\"><strong>&ldquo;Impact Hot Brand&rdquo;</strong></span>，连续十年获得<strong><span style=\"color: #ff6600;\">&ldquo;GROWTH BRAND AWARD&rdquo;</span></strong>！</p>\n<p style=\"text-align: justify;\">&nbsp;</p>\n<p style=\"text-align: justify;\">&nbsp;</p>\n<p style=\"text-align: justify;\">此款Prosecco属于酒庄的Luxury奢华系列，也是酒庄的进阶款，同基础款火遍各大葡萄酒市场、INS等主流媒体，更是斩获<em><span style=\"color: #ff0000;\">国际葡萄酒大奖（Mundus Vini）金奖、比利时布鲁塞尔国际葡萄酒大奖赛(Concours&nbsp;Mondial&nbsp;de&nbsp;Bruxelles)金奖、国际葡萄酒与烈酒大赛（IWSC）银奖</span></em>等诸多大奖，品质不俗。</p>\n<p style=\"text-align: justify;\">&nbsp;</p>\n<p style=\"text-align: justify;\">&nbsp;</p>\n<p style=\"text-align: justify;\">酒款由Valdobbiadene Prosecco Superiore DOCG地区的葡萄精心酿制而成，展现出Glera葡萄的绝佳表现，酒液呈现出独特、明亮的稻草黄色，起泡细腻持久，散发出丰富的花香和苹果、桃子和微妙的柑橘香气，口感清爽愉悦，冰镇后更是一绝！</p>\n<p style=\"text-align: justify;\">&nbsp;</p>\n<p style=\"text-align: justify;\">&nbsp;</p>\n<p style=\"text-align: justify;\">值得注意的是，此次酒云上线，早鸟好价只要百元出头便可领略意大利高水准起泡，又因酒云是Mionetto的中国独家代理，以后想喝优质、性价比出众的高品质Prosecco，当然要认准酒云！</p>\n<p style=\"text-align: justify;\">&nbsp;</p>\n<p style=\"text-align: justify;\"><span style=\"color: #ff0000;\"><strong><span style=\"color: #000000;\">意大利Prosecco更加严格的原产地：</span>Valdobbiadene Prosecco Superiore DOCG</strong></span></p>\n<p style=\"text-align: justify;\">&nbsp;</p>\n<p style=\"text-align: justify;\">&nbsp;</p>\n<p style=\"text-align: justify;\">在意大利，DOCG 产区除需要满足DOC产区法规以外，还需要在DOCG产区范围内装瓶，并通过农业部的品尝鉴定，才能标注为DOCG。普罗塞克产区拥有 2个DOCG 产区： Asolo和Conegliano Valdobbiadene，其中后者还包含多个子产区。</p>\n<p style=\"text-align: justify;\">&nbsp;</p>\n<p style=\"text-align: justify;\">&nbsp;</p>\n<p style=\"text-align: justify;\">Valdobbiadene产区其实一直隶属于Prosecco DOC这个庞大的产区体系，但它在2009年被认证为DOCG，而且最近还被联合国教科文组织（UNESCO）列入了世界遗产名录。这里出产的Prosecco通常品质更高，更能代表该产区的风土，几乎只使用传统的意大利发酵法，酿酒品种也以Glera葡萄为主。</p>\n<p style=\"text-align: center;\">&nbsp;</p>\n<p style=\"text-align: justify;\">&nbsp;</p>\n<p style=\"text-align: justify;\">&nbsp;</p>\n<p style=\"text-align: justify;\"><strong><span style=\"background-color: #ff6600; color: #ffffff;\">全球起泡巨头旗下，意大利Prosecco领头羊！</span></strong></p>\n<p style=\"text-align: justify;\">&nbsp;</p>\n<p style=\"text-align: justify;\">创立于1887年的Mionetto酒庄，多年来一直是意大利普罗塞克的<strong>领头羊</strong>，也是全球出口量领先的<strong>国际普罗塞克品牌</strong>，以及国际舞台上的普罗塞克举足轻重的品牌，实力有目可睹。</p>\n<p style=\"text-align: justify;\">&nbsp;</p>\n<p style=\"text-align: justify;\">&nbsp;</p>\n<p style=\"text-align: justify;\">酒庄的酒标倾斜的度数是27&deg;，27&deg;是普罗塞克醉初的葡萄园的坡度，也代表了酒庄酿造普罗塞克的初心，百年如一日，始终坚持以酿造醉好的普罗塞克为己任，加上醉现代的酿造技术，才能成就如此完美的普罗塞克。</p>\"}",
		Content: "你是专业的酒行业文案写作大师，根据以下提供的［文案写作规范］和USER提供的［关键信息］来生成一篇专业商品详情文案(文案里不能出现卖价和任何售卖链接)。生成的文案内容请确保保留换行符（\\n），并最终以 JSON 格式返回. \n\n[文案写作规范］：\n\n标题：【括号内汉字不超过10个】酒款中文名+卖点信息：如直降 秒杀 低均(市场均价和卖价得到) 等等+ 酒款原文名\n\n副标题：3个最重要的卖点，不超过3句话，24个汉字。如名庄，高分，好年份，好园子, 好价格 等等。\n\n正文(1000字左右):\n第一段话：将副标题展开来写。(最少100字) \n\n第二段话：选择最突出的一个特点/卖点进行商品的介绍。(年份好就介绍年份，园子好就介绍园子，酿酒师强就介绍酿酒师，如果都没有，就找高分酒评来描述酒款。)(最少200字)\n\n第三段话：介绍酒庄及引用酒评家对酒庄的好评。(最少400字)\n\n[示例]:{\"title\":\"【酒王巴罗洛缔造者】巴罗洛侯爵千年巴罗洛干红葡萄酒1990腰斩均价 Marchesi di Barolo Barolo Millennium Antichi Poderi 1990\",\"subtitle\":\"产区缔造者，始祖级酒庄，珍稀老年份“千禧年”！\",\"first_paragraph\":\"“王者之酒，酒中之王(Wine of the King, King of the Wine)”的终极出处，Barolo产区的缔造者，现代Barolo始祖级酒庄——巴罗洛侯爵Marchesi di Barolo重磅回归！\n\n这次带来的是酒庄非常珍贵少见的“千禧年”Barolo Millennium 1990年份纪念版“Barolo”！值得珍藏的老年份，配额非常有限，腰斩WS均价，非常给力！\",\"second_paragraph\":\"丨百年传承·Barolo产区奠基人\n\n从1929年开始，Marchesi di Barolo转移到Abbona家族手中，在Pietro Abbona与兄弟姐妹的共同努力下，酒庄寻回了古老的酿造工艺，并提升了酿酒技术。Pietro Abbona是酿造巴罗洛葡萄酒的先锋，是他带领巴罗洛产区的葡萄酒走向世界，享誉全球。\n\n时至今日，Marchesi di Barolo已在Abbona家族中传承了六代人。酒庄至今保存着侯爵夫人遗留下来的巨大橡木桶并仍在使用，同时依然坚持不懈地致力于酿造高品质的葡萄酒，而这些酿酒技术、葡萄种植法以及葡萄酒文化也将随着家族不断传承下去。\",\"third_paragraph\":\"丨酒中之王“Barolo”的缔造者\n\nMarchesi di Barolo巴罗洛侯爵酒庄位于意大利Piedmont的巴罗洛Barolo产区，这里曾是巴罗洛侯爵的世袭领地。酒庄的历史可追溯到1861年，为末代侯爵Marquis Falletti的夫人所建立，因酿造出真正意义上的DI一瓶现代（干型）Barolo而闻名遐迩！\n\n牧师多梅尼科·马塞（Domenico Masse）曾经在他的《巴罗洛之城》一书中明确写道：“这种类型葡萄酒的创造，应归功在19世纪初的Falletti家族（Marquis意为侯爵）名下，是他们在自己巴罗洛的酒庄内精心酿造出来的。”\n\n酒庄从本村以及周边各村庄购入至为优秀的内比奥罗葡萄，酿出美酒供给皇家与上流社会，据说国王一年365日每天都能品尝到一瓶新的Barolo酒，而更多的酒醉终又借宫廷的威望行销各地，这就是Barolo被称为“王者之酒，酒中之王”的原因。而这些村庄则变成了今天的Barolo DOCG保护产区。\n\n酒庄拥有得天独厚的风土，适宜的土壤以及阳光充足的热量为葡萄的生长提供了理想的环境，酒庄种植有桑娇维塞、内比奥罗和科罗里诺等葡萄品种，出产有单一园系列、传统系列等多种葡萄酒，曾凭借优秀品质多次获奖。\"}",
	}, openai.ChatCompletionMessage{
		Role:    openai.ChatMessageRoleUser,
		Content: "[关键信息］：\n" + req.Text,
	})

	completion, err := l.svcCtx.ChatGpt3.CreateChatCompletion(context.Background(), openai.ChatCompletionRequest{
		Model:          "gpt-4o",
		Messages:       message,
		ResponseFormat: &openai.ChatCompletionResponseFormat{Type: "json_object"},
		Temperature:    0.2,
	})

	if err != nil {
		logx.Error(fmt.Sprintf("request chatGpt err %v", err.Error()))
		return nil, xerr.NewErrMsg(err.Error())
	}

	_, _ = l.svcCtx.ArticleGenerationModel.Insert(l.ctx, &model.VhArticleGeneration{
		OpId:      req.OpId,
		OpName:    req.OpName,
		ShortCode: req.ShortCode,
		Param:     req.Text,
		Result:    completion.Choices[0].Message.Content,
	})

	var gen generator

	err = json.Unmarshal([]byte(completion.Choices[0].Message.Content), &gen)
	if err != nil {
		logx.Error("CpywritingGeneration json.Unmarshal err:" + err.Error())
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "生成文案失败")
	}

	resp = &types.CpywritingGenerationResp{
		Title:    gen.Title,
		Subtitle: gen.Subtitle,
		Text:     fmt.Sprintf("%s\n\n\n%s\n\n\n%s", gen.FirstParagraph, gen.SecondParagraph, gen.ThirdParagraph),
	}

	return
}
