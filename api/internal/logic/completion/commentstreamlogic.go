package completion

import (
	"context"
	"encoding/json"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common"
	"engine/common/model"
	"engine/common/openai"
	"engine/common/result"
	"errors"
	"fmt"
	"github.com/zeromicro/go-zero/core/logx"
	"io"
	"io/ioutil"
	"net/http"
	"time"
)

type CommentStreamLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCommentStreamLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CommentStreamLogic {
	return &CommentStreamLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CommentStreamLogic) CommentStream(req *types.CommentStreamReq, w http.ResponseWriter, f http.Flusher) error {
	req.UseDs = true
	req.UseGpt = false
	if !req.UseDs && !req.UseGpt {
		return errors.New("请至少选择一个平台使用")
	}

	var (
		err        error
		completion *openai.ChatCompletionStream
	)

	if req.Pid == 0 {
		return errors.New("期数不能为空")
	}

	goodsResp, err := http.Get(fmt.Sprintf("https://images.vinehoo.com/vinehoo/client/commodities/periods/%d.json", req.Pid))
	if err != nil {
		return errors.New("获取期数信息失败")
	}
	defer goodsResp.Body.Close()

	if goodsResp.StatusCode != 200 {
		return errors.New("无效的期数")
	}
	body, err := ioutil.ReadAll(goodsResp.Body)

	type goods struct {
		Data struct {
			Detail        string `json:"detail"`
			Brief         string `json:"brief"`
			PackagePrices string `json:"package_prices"`
		} `json:"data"`
	}

	var g goods
	err = json.Unmarshal(body, &g)
	if err != nil {
		return errors.New("获取期数内容信息失败")
	}

	systemMessage := openai.ChatCompletionMessage{
		Role:    openai.ChatMessageRoleSystem,
		Content: req.Prompt,
	}
	message := []openai.ChatCompletionMessage{
		systemMessage,
		{
			Role: openai.ChatMessageRoleUser,
			Content: fmt.Sprintf(`
商品信息:
	标题：%s
	副标题：%s
	售价：%f
	内容：%s
`, req.Title, g.Data.Brief, g.Data.PackagePrices, common.HTMLToMarkdown(g.Data.Detail)),
		},
	}

	resp := new(types.CommentResp)

	if req.UseDs {
		completion, err = l.svcCtx.DeepSeekComment.CreateChatCompletionStream(l.ctx, openai.ChatCompletionRequest{
			Model:       "deepseek-reasoner",
			Messages:    message,
			Temperature: req.Temperature + 0.5,
		})

	} else if req.UseGpt {
		completion, err = l.svcCtx.ChatGpt3.CreateChatCompletionStream(l.ctx, openai.ChatCompletionRequest{
			Model:       "gpt-4o",
			Messages:    message,
			Temperature: req.Temperature,
		})
	}

	defer func() {
		completion.Close()
	}()

	if err != nil {
		erMsg := fmt.Sprintf("request CommentStream err %v", err.Error())
		l.Logger.Error(erMsg)
		return errors.New(erMsg)
	}

	for {
		v, er := completion.Recv()
		if er != nil {
			if errors.Is(er, io.EOF) || errors.Is(er, errors.New("stream ID 3; INTERNAL_ERROR")) {
				if er != io.EOF {
					l.Logger.Error(fmt.Sprintf("chat Read Stream err %s", er))
				}
				break
			}
		} else {
			msg := ""
			isThink := false
			if req.UseDs {
				if len(v.Choices[0].Delta.ReasoningContent) > 0 {
					msg = v.Choices[0].Delta.ReasoningContent
					resp.DsThink += msg
					isThink = true
				} else {
					msg = v.Choices[0].Delta.Content
					resp.DsContent += msg
				}
			} else if req.UseGpt {
				msg = v.Choices[0].Delta.Content
				resp.GptContent += msg
			}

			if req.UseDs {
				runes := []rune(msg) // 将字符串转换为 rune 切片，处理多字节字符
				step := 1
				for i := 0; i < len(runes); i += step {
					end := i + step
					if end > len(runes) {
						end = len(runes)
					}
					chunk := string(runes[i:end])
					jsonData, _ := json.Marshal(map[string]interface{}{
						"msg":      chunk,
						"is_think": isThink,
					})
					result.SSESuccess(w, f, string(jsonData))
					time.Sleep(time.Millisecond * time.Duration(req.WaitTime))
				}
			} else {
				jsonData, _ := json.Marshal(map[string]interface{}{
					"msg":      msg,
					"is_think": isThink,
				})
				result.SSESuccess(w, f, string(jsonData))
			}
		}
	}

	reqByte, _ := json.Marshal(req)
	respByte, _ := json.Marshal(resp)
	_, er := l.svcCtx.CommentGenerationModel.Insert(l.ctx, &model.VhCommentGeneration{
		Req:  string(reqByte),
		Resp: string(respByte),
	})
	if er != nil {
		l.Logger.Error(fmt.Sprintf("streamCommentGenerationModel.Insert err %v", er.Error()))
	}

	return nil
}
