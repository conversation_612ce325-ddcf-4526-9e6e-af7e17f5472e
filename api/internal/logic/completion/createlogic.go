package completion

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/openai"
	"engine/common/result"
	"engine/common/xerr"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateLogic {
	return &CreateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateLogic) Create(req *types.CompletionCreateReq, w http.ResponseWriter, f http.Flusher) error {
	one, err := l.svcCtx.TextEmbeddingAda002Model.FindOne(l.ctx, req.Eid)
	if err != nil && err != model.ErrNotFound {
		return xerr.NewErrCode(xerr.DbError)
	}

	if err == model.ErrNotFound {
		return errors.New("数据不存在")
	}

	var message []openai.ChatCompletionMessage
	message = append(message, openai.ChatCompletionMessage{
		Role:    openai.ChatMessageRoleAssistant,
		Content: "请在回答问题时先参考我提供的内容",
		//Content: "请根据我提供的内容进行分析和回答",
		//Content: one.Content,
	})
	message = append(message, openai.ChatCompletionMessage{
		Role:    openai.ChatMessageRoleUser,
		Content: fmt.Sprintf("内容：\\n\\n%s\\n\\n问题:%s\\n回答:", one.Content, req.Text),
	})
	completion, er := l.svcCtx.ChatGpt.CreateChatCompletionStream(context.Background(), openai.ChatCompletionRequest{
		Model:    openai.GPT3Dot5Turbo,
		Stream:   true,
		Messages: message,
	})

	defer func() {
		completion.Close()
	}()

	if er != nil {
		logx.Error(fmt.Sprintf("request chatGpt err %e", er))
		return errors.New("服务繁忙,请稍后再试")
	}

	var respContent string
	for {
		v, er := completion.Recv()
		if er != nil {
			if errors.Is(er, io.EOF) || errors.Is(er, errors.New("stream ID 3; INTERNAL_ERROR")) {
				if er != io.EOF {
					logx.Error(fmt.Sprintf("chat Read Stream err %s", er))
				}
				break
			}
		} else {
			respContent += v.Choices[0].Delta.Content
			v.Choices[0].Delta.Content = strings.Replace(v.Choices[0].Delta.Content, " ", "&nbsp;&nbsp;", -1)
			result.SSESuccess(w, f, strings.Replace(v.Choices[0].Delta.Content, "\n", "<div class=\"line_feed\"></div>", -1))
		}
	}

	_, e := l.svcCtx.QaLogModel.Insert(l.ctx, &model.VhQaLog{
		Eid:         req.Eid,
		Category:    req.Category,
		Uid:         req.Uid,
		ReqContent:  req.Text,
		RespContent: respContent,
	})

	if e != nil {
		logx.Error("QaLogModel.Insert err %e", e)
	}

	return nil
}
