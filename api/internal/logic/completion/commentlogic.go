package completion

import (
	"context"
	"encoding/json"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/openai"
	"engine/common/xerr"
	"errors"
	"fmt"
	"golang.org/x/sync/errgroup"

	"github.com/zeromicro/go-zero/core/logx"
)

type CommentLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCommentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CommentLogic {
	return &CommentLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CommentLogic) Comment(req *types.CommentReq) (resp *types.CommentResp, err error) {
	resp = new(types.CommentResp)
	if !req.UseDs && !req.UseGpt {
		return
	}

	var wait errgroup.Group
	systemMessage := openai.ChatCompletionMessage{
		Role:    openai.ChatMessageRoleSystem,
		Content: req.Prompt,
		//Content: "你是一位商品评论生成专家，根据用户给到的‘提示词’和‘标题’来生成评论，并且根据给到的‘最近评论’模仿口吻(最近评论有数据时)。",
	}
	//关闭最近评论
	req.LatestComments = ""
	message := []openai.ChatCompletionMessage{
		systemMessage,
		{
			Role:    openai.ChatMessageRoleUser,
			Content: req.Title,
			//Content: fmt.Sprintf("提示词:%s\n标题:%s\n最近评论:%s", req.Prompt, req.Title, req.LatestComments),
		},
	}
	if req.UseDs {
		wait.Go(func() error {
			completion, er := l.svcCtx.DeepSeekComment.CreateChatCompletion(l.ctx, openai.ChatCompletionRequest{
				Model: "xdeepseekr1",
				//Model: "xdeepseekv3",
				//Model:       "deepseek-chat",
				Messages: message,
				//Temperature: 1.5,
				Temperature: req.Temperature,
			})

			if er != nil {
				erMsg := fmt.Sprintf("request DeepSeekComment err %v", er.Error())
				logx.Error(erMsg)
				return errors.New(erMsg)
			}

			resp.DsContent = completion.Choices[0].Message.Content
			resp.DsThink = completion.Choices[0].Message.ReasoningContent
			return nil
		})
	}
	if req.UseGpt {
		wait.Go(func() error {
			completion, er := l.svcCtx.ChatGpt3.CreateChatCompletion(l.ctx, openai.ChatCompletionRequest{
				Model:       "gpt-4o",
				Messages:    message,
				Temperature: req.Temperature,
			})

			if er != nil {
				erMsg := fmt.Sprintf("request ChatGpt3 err %v", er.Error())
				logx.Error(erMsg)
				return errors.New(erMsg)
			}

			resp.GptContent = completion.Choices[0].Message.Content
			return nil
		})
	}
	err = wait.Wait()
	if err != nil {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, err.Error())
	}

	reqByte, _ := json.Marshal(req)
	respByte, _ := json.Marshal(resp)
	_, er := l.svcCtx.CommentGenerationModel.Insert(l.ctx, &model.VhCommentGeneration{
		Req:  string(reqByte),
		Resp: string(respByte),
	})
	if er != nil {
		l.Logger.Error(fmt.Sprintf("CommentGenerationModel.Insert err %v", er.Error()))
	}

	return
}
