package completion

import (
	"context"
	"engine/common/openai"
	"engine/common/xerr"
	"fmt"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type TextLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewTextLogic(ctx context.Context, svcCtx *svc.ServiceContext) *TextLogic {
	return &TextLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *TextLogic) Text(req *types.CompletionTextReq) (resp *types.CompletionTextResp, err error) {
	var message []openai.ChatCompletionMessage
	message = append(message, openai.ChatCompletionMessage{
		Role:    openai.ChatMessageRoleUser,
		Content: "你是一个工单内容的总结助手。\n根据提供的工单文本内容总结一句话作为工单的标题。\n\n给出标题后判断一下是否符合中文表达规范，如果不符合则进行优化，只返回工单标题，禁止出现和标题无关的内容。 内容是```" + req.Text + "```",
		//Content: "从以下内容中生成一个反馈类型的标题,如果不能生成时,截取内容的前20个文字作为标题。\n 内容是```" + req.Text + "```",
	})

	/*message = append(message, openai.ChatCompletionMessage{
		Role:    openai.ChatMessageRoleUser,
		Content: req.Text,
	})*/

	completion, err := l.svcCtx.ChatGpt.CreateChatCompletion(context.Background(), openai.ChatCompletionRequest{
		Model:       openai.GPT3Dot5Turbo,
		Messages:    message,
		Temperature: 0.1,
	})

	if err != nil {
		logx.Error(fmt.Sprintf("request chatGpt err %v", err.Error()))
		return nil, xerr.NewErrMsg(err.Error())
	}

	resp = new(types.CompletionTextResp)
	resp.Text = completion.Choices[0].Message.Content

	return
}
