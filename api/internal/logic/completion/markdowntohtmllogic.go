package completion

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/openai"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type MarkdownToHtmlLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewMarkdownToHtmlLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MarkdownToHtmlLogic {
	return &MarkdownToHtmlLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *MarkdownToHtmlLogic) MarkdownToHtml(req *types.MarkdownToHtmlReq) (resp *types.MarkdownToHtmlResp, err error) {
	resp = new(types.MarkdownToHtmlResp)

	systemMessage := openai.ChatCompletionMessage{
		Role: openai.ChatMessageRoleSystem,
		Content: `

`,
	}

	message := []openai.ChatCompletionMessage{
		systemMessage,
		{
			Role:    openai.ChatMessageRoleUser,
			Content: req.Info,
		},
	}

	completion, er := l.svcCtx.DeepSeekMarkdownToHtml.CreateChatCompletion(l.ctx, openai.ChatCompletionRequest{
		Model:       "deepseek-chat",
		Temperature: 0.5,
		Messages:    message,
		MaxTokens:   5000,
	})

	if er != nil {
		l.Logger.Errorf("DeepSeekMarkdownToHtml CreateChatCompletion err: %v", er)
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, er.Error())
	}

	resp.Info = completion.Choices[0].Message.Content

	return
}
