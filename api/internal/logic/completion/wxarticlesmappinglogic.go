package completion

import (
	"context"
	"engine/common/model"
	"engine/common/xerr"
	"fmt"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type WxArticlesMappingLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewWxArticlesMappingLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WxArticlesMappingLogic {
	return &WxArticlesMappingLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *WxArticlesMappingLogic) WxArticlesMapping(req *types.WxArticlesMappingReq) (resp *types.WxArticlesMappingResp, err error) {
	one, er := l.svcCtx.WxArticlesMappingModel.FindOne(l.ctx, req.Eid)

	if er != nil && er != model.ErrNotFound {
		logx.Error(fmt.Sprintf("WxArticlesMappingLogic WxArticlesMapping WxArticlesMappingModel.FindOne err %e", err))
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	resp = new(types.WxArticlesMappingResp)
	if er != model.ErrNotFound {
		resp.LinkUrl = one.LinkUrl
	}

	return
}
