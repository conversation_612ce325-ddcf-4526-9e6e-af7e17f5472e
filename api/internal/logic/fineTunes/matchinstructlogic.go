package fineTunes

import (
	"context"
	"encoding/json"
	"engine/common/openai"
	"engine/common/xerr"
	"fmt"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type MatchInstructLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewMatchInstructLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MatchInstructLogic {
	return &MatchInstructLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *MatchInstructLogic) MatchInstruct(req *types.FineTunesMatchInstructReq) (resp *types.FineTunesMatchInstructResp, err error) {
	var message []openai.ChatCompletionMessage
	message = append(message, openai.ChatCompletionMessage{
		Role:    openai.ChatMessageRoleSystem,
		Content: "json指令机器人",
	}, openai.ChatCompletionMessage{
		Role:    openai.ChatMessageRoleUser,
		Content: req.Text,
	})

	completion, err := l.svcCtx.ChatGpt.CreateChatCompletion(context.Background(), openai.ChatCompletionRequest{
		Model:          req.Model,
		Messages:       message,
		ResponseFormat: &openai.ChatCompletionResponseFormat{Type: "json_object"},
		Temperature:    0.2,
	})
	/*completion, err := l.svcCtx.ChatGpt.CreateCompletion(l.ctx, openai.CompletionRequest{
		Model: req.Model,
		//Prompt: fmt.Sprintf("Returning a blank answer when the answer to the question cannot be found, question：'''%s'''\n\n###\n\n", req.Text),
		Prompt:      fmt.Sprintf("%s\n\n###\n\n", req.Text),
		MaxTokens:   600,
		Temperature: 0.01,
		//TopP:      0.1,
		Stop:   []string{"###"},
		BestOf: 1,
	})*/
	if err != nil {
		return nil, xerr.NewErrMsg(fmt.Sprintf("服务错误 e:%s", err.Error()))
	}

	resp = new(types.FineTunesMatchInstructResp)
	if completion.Choices[0].Message.Content == "" {
		return
	}

	resp.Result = []interface{}{}
	var instructs types.FineTunesInstructs
	fmt.Println(completion.Choices[0].Message.Content)
	er := json.Unmarshal([]byte(completion.Choices[0].Message.Content), &instructs)
	if er != nil {
		logx.Error("MatchInstructLogic MatchInstruct Unmarshal err:" + er.Error())
		return
	}

	resp.IsInstruct = 1
	resp.Result = instructs

	return
}
