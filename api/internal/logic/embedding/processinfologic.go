package embedding

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ProcessInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewProcessInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ProcessInfoLogic {
	return &ProcessInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ProcessInfoLogic) ProcessInfo() (resp *types.EmbeddingProcessResp, err error) {
	resp = new(types.EmbeddingProcessResp)
	var list []*types.EmbeddingProcessPLog
	for name, s := range l.svcCtx.PLog {
		list = append(list, &types.EmbeddingProcessPLog{
			Name:    name,
			Total:   s.Total,
			Success: s.Success,
			Fail:    s.<PERSON>ail,
			FailEid: s.FailEid,
		})
	}
	resp.List = list
	return
}
