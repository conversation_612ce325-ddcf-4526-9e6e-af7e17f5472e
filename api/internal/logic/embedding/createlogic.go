package embedding

import (
	"context"
	"encoding/base64"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/xerr"
	"engine/common/xredis"
	"fmt"
	"github.com/gomodule/redigo/redis"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateLogic {
	return &CreateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateLogic) Create(req *types.EmbeddingCreateReq) error {
	logx.Info(fmt.Sprintf("eid:%d", req.Eid))

	_, err := xredis.Do(l.svcCtx.Redis, func(conn redis.Conn) (interface{}, error) {
		return conn.Do("LPUSH", fmt.Sprintf("embeddings_process"),
			fmt.Sprintf(`{"eid":%d,"category":"%s","text":"%s"}`, req.Eid, req.Category, base64.StdEncoding.EncodeToString([]byte(req.Text))))
	})
	if err != nil {
		return xerr.NewErrMsg(err.Error())
	}

	l.svcCtx.Mutex.Lock()
	l.svcCtx.PLog[req.Category].Total++
	l.svcCtx.Mutex.Unlock()

	return nil
}
