package embedding

import (
	"context"
	"encoding/binary"
	"encoding/json"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/openai"
	"engine/common/xerr"
	"fmt"
	"github.com/Masterminds/squirrel"
	"github.com/RediSearch/redisearch-go/v2/redisearch"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/logx"
	"math"
	"strings"
)

type SuggestionLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSuggestionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SuggestionLogic {
	return &SuggestionLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SuggestionLogic) Suggestion(req *types.EmbeddingSuggestionReq) (resp *types.EmbeddingSuggestionResp, err error) {
	resp = new(types.EmbeddingSuggestionResp)
	resp.List = make([]*types.EmbeddingEmbeddingSuggestion, 0)

	var em []float32
	var raw string
	top := 5
	switch req.Genre {
	case "suggestion":
		if req.Eid < 1 || req.Category == "" {
			return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "数据源id或数据源类型不能为空")
		}
		//根据eid推荐
		one, err := l.svcCtx.TextEmbeddingAda002Model.FindOneByEidCategory(context.Background(), req.Eid, req.Category)
		if err != nil && err != model.ErrNotFound {
			logx.Error(fmt.Sprintf("SuggestionLogic Suggestion TextEmbeddingAda002Model.FindOneByEidCategory err %e", err))
			return nil, xerr.NewErrCode(xerr.DbError)
		}
		if err == model.ErrNotFound {
			return nil, xerr.NewErrCode(xerr.DataNoExistError)
		}

		err = json.Unmarshal([]byte(one.Embeddings), &em)
		if err != nil {
			logx.Error(fmt.Sprintf("json.Unmarshal([]byte(one.Embeddings) err %e", err))
			return nil, xerr.NewErrMsg(err.Error())
		}

		raw = fmt.Sprintf("(-@eid:[%d,%d])=>[KNN %d @embedding $vector AS vector_score]", req.Eid, req.Eid, top)
		//raw = fmt.Sprintf("(-@eid:[%d,%d] -@category:%s)=>[KNN %d @embedding $vector AS vector_score]", req.Eid, req.Eid, req.Category, top)

		break
	case "qa":
		if req.Query == "" {
			return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "搜索内容不能为空")
		}
		//根据搜索内容推荐
		embeddings, err := l.svcCtx.ChatGpt.CreateEmbeddings(l.ctx, openai.EmbeddingRequest{
			Input: []string{
				req.Query,
			},
			Model: openai.AdaEmbeddingV2,
		})
		if err != nil {
			logx.Error(fmt.Sprintf("ChatGpt CreateEmbeddings err %e", err))
			return nil, xerr.NewErrMsg(err.Error())
		}

		em = embeddings.Data[0].Embedding

		raw = fmt.Sprintf("*=>[KNN %d @embedding $vector AS vector_score]", top)

		break
	default:
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "不支持的类型:"+req.Genre)
	}

	//搜索向量
	vectorBytes := ""
	for _, v := range em {
		bits := math.Float32bits(v)
		bytes := make([]byte, 4)
		binary.LittleEndian.PutUint32(bytes, bits)
		vectorBytes += string(bytes)
	}
	search, _, err := l.svcCtx.TextEmbeddingAda002Search.
		Search(redisearch.NewQuery(raw).
			AddParam("vector", vectorBytes).
			SetReturnFields("eid", "category", "vector_score").SetSortBy("vector_score", false).SetDialect(2))
	if err != nil {
		logx.Error(fmt.Sprintf("TextEmbeddingAda002Search Search err %e", err))
		return nil, xerr.NewErrMsg(err.Error())
	}

	var ids []int
	for _, document := range search {
		ids = append(ids, cast.ToInt(strings.Replace(document.Id, "embeddings:", "", 1)))
	}

	if len(ids) > 0 {
		var results []*model.VhTextEmbeddingAda002ByEidCategoryContent
		err := l.svcCtx.TextEmbeddingAda002Model.FindRows(l.ctx, squirrel.Select("eid", "category", "content").From(l.svcCtx.TextEmbeddingAda002Model.TableName()).Where(squirrel.Eq{"id": ids}), &results)
		if err != nil {
			logx.Error(fmt.Sprintf("SuggestionLogic Suggestion TextEmbeddingAda002Model.FindRows err %e", err))
			return nil, xerr.NewErrCode(xerr.DbError)
		}

		if len(results) > 0 {
			for _, result := range results {
				resp.List = append(resp.List, &types.EmbeddingEmbeddingSuggestion{
					Eid:      result.Eid,
					Category: result.Category,
					Content:  result.Content,
				})
			}
		}
	}

	return
}
