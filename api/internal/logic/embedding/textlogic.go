package embedding

import (
	"context"
	"engine/common/openai"
	"engine/common/xerr"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type TextLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewTextLogic(ctx context.Context, svcCtx *svc.ServiceContext) *TextLogic {
	return &TextLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *TextLogic) Text(req *types.EmbeddingTextReq) (resp *types.EmbeddingTextResp, err error) {
	model := openai.AdaEmbeddingV2
	if req.Model == 2 {
		model = openai.AdaEmbeddingV3Large
	}
	embeddings, err := l.svcCtx.ChatGpt.CreateEmbeddings(context.Background(), openai.EmbeddingRequest{
		Input: []string{
			req.Text,
		},
		Model: model,
	})
	if err != nil {
		logx.Error("TextEmbeddingAda002Model CreateEmbeddings err:" + err.Error())
		return nil, xerr.NewErrMsg(err.Error())
	}

	/*text, err := json.Marshal(embeddings.Data[0].Embedding)
	if err != nil {
		logx.Error("embeddings Marshal err:" + err.Error())
		return nil, xerr.NewErrMsg(err.Error())
	}*/

	resp = new(types.EmbeddingTextResp)
	resp.Embedding = embeddings.Data[0].Embedding
	return
}
