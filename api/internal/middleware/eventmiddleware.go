package middleware

import (
	"engine/common/result"
	"engine/common/xerr"
	"net/http"
)

type EventMiddleware struct {
}

func NewEventMiddleware() *EventMiddleware {
	return &EventMiddleware{}
}

func (m *EventMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.<PERSON>er().Set("Access-Control-Allow-Methods", "POST,GET,PUT,DELETE,OPTIONS")

		if _, ok := w.(http.Flusher); !ok {
			result.HttpResult(r, w, nil, xerr.NewErrMsg("此链接不支持流处理"))
			return
		}

		//转为流模式
		w.Header().Set("Content-Type", "text/event-stream")
		w.Header().Set("Cache-Control", "no-cache")
		w.Header().Set("Connection", "keep-alive")
		w.Head<PERSON>().Set("X-Accel-Buffering", "no")           // 禁用缓存
		w.Head<PERSON>().Set("X-Content-Type-Options", "nosniff") // 避免MIME嗅探
		w.Header().Set("X-HTTP-Protocol", "HTTP/2.0")       // 设置协议为HTTP/2

		next(w, r)
	}
}
