// Code generated by goctl. DO NOT EDIT.
package types

type CommentReq struct {
	UseDs          bool    `json:"use_ds"`
	UseGpt         bool    `json:"use_gpt"`
	Prompt         string  `json:"prompt" validate:"required" v:"提示词"`
	Title          string  `json:"title" validate:"required" v:"标题"`
	Temperature    float32 `json:"temperature,default=0.7,optional" v:"温度值"`
	LatestComments string  `json:"latest_comments,optional" v:"最近评论"`
}

type CommentResp struct {
	DsContent  string `json:"ds_content"`
	DsThink    string `json:"ds_think"`
	GptContent string `json:"gpt_content"`
}

type CommentStreamReq struct {
	UseDs       bool    `form:"use_ds,optional"`
	UseGpt      bool    `form:"use_gpt,optional"`
	Pid         int64   `form:"pid"`
	Prompt      string  `form:"prompt" validate:"required" v:"提示词"`
	Title       string  `form:"title" validate:"required" v:"标题"`
	Temperature float32 `form:"temperature,default=0.7,optional" v:"温度值"`
	WaitTime    int64   `form:"wait_time,default=25,optional" v:"等待时间"`
}

type CompletionCreateReq struct {
	Text     string `form:"text" validate:"required" v:"需要处理的文字"`
	Eid      int64  `form:"eid" v:"源数据id"`
	Uid      int64  `form:"uid" v:"用户"`
	Category string `form:"category" v:"所属分类"`
}

type CompletionTextReq struct {
	Text string `form:"text" validate:"required" v:"需要处理的文字"`
}

type CompletionTextResp struct {
	CompletionTextReq
}

type CpywritingGenerationReq struct {
	Text      string `json:"text" validate:"required" v:"酒款信息"`
	OpId      string `json:"op_id" validate:"required" v:"操作人id"`
	OpName    string `json:"op_name" validate:"required" v:"操作人名称"`
	ShortCode string `json:"short_code" validate:"required" v:"简码"`
}

type CpywritingGenerationResp struct {
	Title    string `json:"title"`
	Subtitle string `json:"subtitle"`
	Text     string `json:"text"`
}

type CsCreateReq struct {
	Text  string `json:"text" validate:"required" v:"需要处理的文字"`
	Title string `json:"title" validate:"required" v:"标题"`
}

type CsDeleteReq struct {
	Id string `json:"id" validate:"required" v:"id"`
}

type CsQueryReq struct {
	Text string `json:"text" validate:"required" v:"需要检索的文字"`
}

type CsQueryResp struct {
	List []QueryInfo `json:"list"`
}

type CsUpdateReq struct {
	Id string `json:"id" validate:"required" v:"id"`
	CsCreateReq
}

type EmbeddingCreateReq struct {
	Text     string `json:"text" validate:"required" v:"需要处理的文字"`
	Eid      int64  `json:"eid" v:"源数据id"`
	Category string `json:"category" v:"所属分类"`
}

type EmbeddingEmbeddingSuggestion struct {
	Eid      int64  `json:"eid"`
	Category string `json:"category"`
	Content  string `json:"content"`
}

type EmbeddingProcessPLog struct {
	Name    string  `json:"name"`
	Total   int64   `json:"total"`
	Success int64   `json:"success"`
	Fail    int64   `json:"fail"`
	FailEid []int64 `json:"failEid"`
}

type EmbeddingProcessResp struct {
	List []*EmbeddingProcessPLog `json:"list"`
}

type EmbeddingSuggestionReq struct {
	Eid      int64  `form:"eid,optional" v:"源数据id"`
	Category string `form:"category,optional" v:"所属分类"`
	Query    string `form:"query,optional" v:"搜索内容"`
	Genre    string `form:"genre" v:"所属分类" validate:"required"`
}

type EmbeddingSuggestionResp struct {
	List []*EmbeddingEmbeddingSuggestion `json:"list"`
}

type EmbeddingTextReq struct {
	Model int64  `json:"model,default=1"` //2 AdaEmbeddingV3Large
	Text  string `json:"text" validate:"required" v:"需要处理的文字"`
}

type EmbeddingTextResp struct {
	Embedding []float32 `json:"embedding"`
}

type FineTunesInstructInfo struct {
	E        string      `json:"e"`
	Path     string      `json:"p"`
	Data     interface{} `json:"d"`
	Module   string      `json:"m"`
	Genre    string      `json:"t"`
	Analysis string      `json:"a"`
}

type FineTunesInstructs struct {
	Instructs []FineTunesInstructInfo `json:"its"`
}

type FineTunesMatchInstructReq struct {
	Text  string `json:"text" validate:"required" v:"需要处理的文字"`
	Model string `json:"model,default=curie:ft-personal:vinehoo-os-2023-04-03-01-22-14" v:"模型"`
}

type FineTunesMatchInstructResp struct {
	IsInstruct int64       `json:"is_instruct"`
	Result     interface{} `json:"result"`
}

type ForwardReq struct {
	Data interface{} `json:"data"`
}

type ForwardResp struct {
	Result interface{} `json:"result"`
}

type MarkdownToHtmlReq struct {
	Info string `json:"info" validate:"required" v:"需转换的的md数据"`
}

type MarkdownToHtmlResp struct {
	Info string `json:"info"`
}

type QueryInfo struct {
	Id      string  `json:"id"`
	Title   string  `json:"title"`
	Content string  `json:"content"`
	Score   float64 `json:"score"`
}

type WxArticlesMappingReq struct {
	Eid int64 `form:"eid" validate:"min=1" v:"源数据id"`
}

type WxArticlesMappingResp struct {
	LinkUrl string `json:"link_url"`
}
