// Code generated by goctl. DO NOT EDIT.
package handler

import (
	"net/http"

	completion "engine/api/internal/handler/completion"
	cs "engine/api/internal/handler/cs"
	embedding "engine/api/internal/handler/embedding"
	fineTunes "engine/api/internal/handler/fineTunes"
	"engine/api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Event},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/commentStream",
					Handler: completion.CommentStreamHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/create",
					Handler: completion.CreateHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/embedding/v3/completion"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.User},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/comment",
					Handler: completion.CommentHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/copywriting_generation",
					Handler: completion.CpywritingGenerationHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/forward",
					Handler: completion.ForwardHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/markdownToHtml",
					Handler: completion.MarkdownToHtmlHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/text",
					Handler: completion.TextHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/wx_articles_mapping",
					Handler: completion.WxArticlesMappingHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/embedding/v3/completion"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/create",
					Handler: cs.CreateHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/delete",
					Handler: cs.DeleteHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/query",
					Handler: cs.QueryHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/update",
					Handler: cs.UpdateHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/embedding/v3/cs"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.User},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/create",
					Handler: embedding.CreateHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/processInfo",
					Handler: embedding.ProcessInfoHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/suggestion",
					Handler: embedding.SuggestionHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/text",
					Handler: embedding.TextHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/embedding/v3/embedding"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/matchInstruct",
					Handler: fineTunes.MatchInstructHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/embedding/v3/fineTunes"),
	)
}
