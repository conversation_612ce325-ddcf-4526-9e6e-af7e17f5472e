package fineTunes

import (
	"engine/api/internal/logic/fineTunes"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func MatchInstructHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.FineTunesMatchInstructReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := fineTunes.NewMatchInstructLogic(r.Context(), svcCtx)
		resp, err := l.MatchInstruct(&req)
		result.HttpResult(r, w, resp, err)
	}
}
