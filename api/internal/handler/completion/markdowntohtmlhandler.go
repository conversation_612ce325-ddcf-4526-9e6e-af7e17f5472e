package completion

import (
	"engine/common/result"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"engine/api/internal/logic/completion"
	"engine/api/internal/svc"
	"engine/api/internal/types"
)

func MarkdownToHtmlHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.MarkdownToHtmlReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := completion.NewMarkdownToHtmlLogic(r.Context(), svcCtx)
		resp, err := l.MarkdownToHtml(&req)
		result.HttpResult(r, w, resp, err)
	}
}
