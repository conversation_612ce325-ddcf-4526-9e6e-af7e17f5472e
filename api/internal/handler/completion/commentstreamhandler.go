package completion

import (
	"engine/api/internal/logic/completion"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func CommentStreamHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.CommentStreamReq
		f, _ := w.(http.Flusher)

		if err := httpx.Parse(r, &req); err != nil {
			result.SSE_PARAM_ERROR(w, f, err.Error())
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.SSE_PARAM_ERROR(w, f, err.Error())
			return
		}

		l := completion.NewCommentStreamLogic(r.Context(), svcCtx)
		err := l.CommentStream(&req, w, f)
		if err != nil {
			result.SSEError(w, f, err.Error())
		} else {
			result.SSEEvent(w, f, result.SSE_EVENT_CLOSE, "")
		}
	}
}
