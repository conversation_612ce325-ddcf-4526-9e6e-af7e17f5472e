package embedding

import (
	"engine/api/internal/logic/embedding"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func SuggestionHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.EmbeddingSuggestionReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := embedding.NewSuggestionLogic(r.Context(), svcCtx)
		resp, err := l.Suggestion(&req)
		result.HttpResult(r, w, resp, err)
	}
}
