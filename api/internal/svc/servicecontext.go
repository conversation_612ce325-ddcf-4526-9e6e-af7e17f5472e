package svc

import (
	"context"
	"encoding/base64"
	"encoding/binary"
	"encoding/json"
	"engine/api/internal/middleware"
	"engine/common/alvectoor"
	cf "engine/common/config"
	"engine/common/model"
	"engine/common/openai"
	"engine/common/validation"
	"fmt"
	"math"
	"net/http"
	"sync"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/RediSearch/redisearch-go/v2/redisearch"
	"github.com/gomodule/redigo/redis"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/rest"
)

type ServiceContext struct {
	Config                    cf.ApiConfig
	Verify                    *validation.Verify
	ChatGpt                   *openai.Client
	ChatGpt2                  *openai.Client
	ChatGpt3                  *openai.Client
	DeepSeekComment           *openai.Client
	DeepSeekMarkdownToHtml    *openai.Client
	Global                    rest.Middleware
	Event                     rest.Middleware
	User                      rest.Middleware
	TextEmbeddingAda002Model  model.VhTextEmbeddingAda002Model
	QaLogModel                model.VhQaLogModel
	WxArticlesMappingModel    model.VhWxArticlesMappingModel
	TransModel                model.TransModel
	Redis                     *redis.Pool
	TextEmbeddingAda002Search *redisearch.Client
	PLog                      map[string]*PLog
	Mutex                     sync.Mutex
	Alvector                  *alvectoor.Client
	EmbedCsModel              model.VhTextEmbeddingCsModel
	ArticleGenerationModel    model.VhArticleGenerationModel
	CommentGenerationModel    model.VhCommentGenerationModel
}

type PLog struct {
	Total   int64
	Success int64
	Fail    int64
	FailEid []int64
}

func NewServiceContext(c cf.ApiConfig) *ServiceContext {
	Mysql := sqlx.NewMysql(c.Mysql.DataSource)

	Log := make(map[string]*PLog)
	for _, v := range model.CategoryById {
		Log[v] = &PLog{}
	}

	s := &ServiceContext{
		Config:   c,
		Verify:   validation.NewVerify(),
		ChatGpt:  GetOpenAiClient(openai.GptUrlV1, c.ChatGptToken),
		ChatGpt2: GetOpenAiClient(openai.GptUrlV1, "***************************************************"),
		ChatGpt3: GetOpenAiClient(openai.GptUrlV1, "********************************************************************************************************************************************************************"),
		//DeepSeekComment: GetOpenAiClient(openai.XFUrl, "sk-rkNls654d7jY1Oq39c5e1364FdCf4102908c127bEbCa0323"),
		DeepSeekComment:        GetOpenAiClient(openai.DsUrl, "sk-ebc3beac98a040bc80465a8931342da9"),
		DeepSeekMarkdownToHtml: GetOpenAiClient(openai.DsUrl, "sk-34f4b0ee43b64d1da716743ede063aec"),
		Global:                 middleware.NewGlobalMiddleware().Handle,
		User:                   middleware.NewUserMiddleware().Handle,
		Event:                  middleware.NewEventMiddleware().Handle,
		Redis: &redis.Pool{
			MaxIdle:   100,
			MaxActive: 200,
			//IdleTimeout: 1000,
			//Wait: true,
			Dial: func() (redis.Conn, error) {
				//return redis.Dial("tcp", "*************:6380", redis.DialPassword(""),
				return redis.Dial("tcp", c.Redis.Host, redis.DialPassword(c.Redis.Pass),
					redis.DialDatabase(0),
					redis.DialConnectTimeout(time.Second), //链接超时
					redis.DialWriteTimeout(time.Second),   //写超时
					redis.DialReadTimeout(time.Second),    //读超时
				)
			},
		},
		TextEmbeddingAda002Model: model.NewVhTextEmbeddingAda002Model(Mysql),
		QaLogModel:               model.NewVhQaLogModel(Mysql),
		WxArticlesMappingModel:   model.NewVhWxArticlesMappingModel(Mysql),
		TransModel:               model.NewTransModel(Mysql),
		PLog:                     Log,
		Mutex:                    sync.Mutex{},
		Alvector:                 alvectoor.NewClient("vrs-cn-lbj3o4h7t0001k.dashvector.cn-hangzhou.aliyuncs.com", "sk-kRtyau4DY7vGAjNSIbBS470vE28O1E8D22336D08411EE873746087163A2E0", "ai_cs"),
		EmbedCsModel:             model.NewVhTextEmbeddingCsModel(Mysql),
		ArticleGenerationModel:   model.NewVhArticleGenerationModel(Mysql),
		CommentGenerationModel:   model.NewVhCommentGenerationModel(Mysql),
	}

	s.TextEmbeddingAda002Search = redisearch.NewClientFromPool(s.Redis, "vh_text_embedding_ada_002")

	go ProcessRun(s, 1)
	go ProcessRun(s, 2)
	go ProcessRun(s, 3)
	go ProcessRun(s, 4)
	go ProcessRun(s, 5)
	return s
}

func GetOpenAiClient(BaseUrl, authToken string) *openai.Client {
	config := openai.DefaultConfig(authToken)
	config.BaseURL = BaseUrl
	config.HTTPClient.Transport = &http.Transport{
		/*Proxy: func(req *http.Request) (*url.URL, error) {
			return url.Parse("http://127.0.0.1:7890")
		},*/
	}
	return openai.NewClientWithConfig(config)
}

func ProcessRun(s *ServiceContext, i int) {
	conn := s.Redis.Get()
	defer func() {
		if e := recover(); e != nil {
			logx.Error(fmt.Sprintf("ProcessRun recover err:%e", e))
		}
		logx.Error(fmt.Sprintf("ProcessRun signout"))
		_ = conn.Close()
	}()

	data := new(model.ProcessData)
	for true {
		str, err := redis.String(conn.Do("RPOP", "embeddings_process"))
		if err != nil && err != redis.ErrNil {
			//其他情况自动重连
			_ = conn.Close()
			conn = s.Redis.Get()
			logx.Error("embeddings_process RPOP err:" + err.Error())
			time.Sleep(time.Minute)
			continue
		} else {
			//没数据的时候暂停10秒再去获取
			if err == redis.ErrNil {
				time.Sleep(time.Second * 10)
				continue
			}
		}

		logx.Info(fmt.Sprintf("embeddings_process RPOP info :%s", str))

		e := json.Unmarshal([]byte(str), data)
		if e != nil {
			logx.Error(fmt.Sprintf("embeddings_process RPOP json.Unmarshal err %s, str:%s", e.Error(), str))
			continue
		}

		decode, err := base64.StdEncoding.DecodeString(data.Text)
		if err != nil {
			logx.Error(fmt.Sprintf("embeddings_process base64.StdEncoding.DecodeString %s, str:%s", err.Error(), str))
			continue
		}
		data.Text = string(decode)

		count, err := s.TextEmbeddingAda002Model.FindCount(context.Background(), model.CountBuilder("id", s.TextEmbeddingAda002Model.TableName()).Where(squirrel.Eq{"eid": data.Eid, "category": data.Category}))
		if err != nil {
			s.Mutex.Lock()
			s.PLog[data.Category].Fail++
			s.PLog[data.Category].FailEid = append(s.PLog[data.Category].FailEid, data.Eid)
			s.Mutex.Unlock()
			logx.Error("TextEmbeddingAda002Model FindCount err:" + err.Error())
			continue
		}

		if count > 0 {
			s.Mutex.Lock()
			s.PLog[data.Category].Success++
			s.Mutex.Unlock()
			continue
		}

		embeddings, err := s.ChatGpt.CreateEmbeddings(context.Background(), openai.EmbeddingRequest{
			Input: []string{
				data.Text,
			},
			Model: openai.AdaEmbeddingV2,
		})
		if err != nil {
			s.Mutex.Lock()
			s.PLog[data.Category].Fail++
			s.PLog[data.Category].FailEid = append(s.PLog[data.Category].FailEid, data.Eid)
			s.Mutex.Unlock()
			logx.Error("TextEmbeddingAda002Model CreateEmbeddings err:" + err.Error())
			continue
		}

		text, err := json.Marshal(embeddings.Data[0].Embedding)
		if err != nil {
			s.Mutex.Lock()
			s.PLog[data.Category].Fail++
			s.PLog[data.Category].FailEid = append(s.PLog[data.Category].FailEid, data.Eid)
			s.Mutex.Unlock()
			logx.Error("embeddings Marshal err:" + err.Error())
			continue
		}
		r, err := s.TextEmbeddingAda002Model.Insert(context.Background(), &model.VhTextEmbeddingAda002{
			Eid:        data.Eid,
			Category:   data.Category,
			Embeddings: string(text),
			Content:    data.Text,
		})
		if err != nil {
			s.Mutex.Lock()
			s.PLog[data.Category].Fail++
			s.PLog[data.Category].FailEid = append(s.PLog[data.Category].FailEid, data.Eid)
			s.Mutex.Unlock()
			logx.Error("TextEmbeddingAda002Model insert err:" + err.Error())
			continue
		}

		Id, _ := r.LastInsertId()

		vectorBytes := ""
		for _, v := range embeddings.Data[0].Embedding {
			bits := math.Float32bits(v)
			bytes := make([]byte, 4)
			binary.LittleEndian.PutUint32(bytes, bits)
			vectorBytes += string(bytes)
		}

		//添加到redis
		ls := []interface{}{
			fmt.Sprintf("embeddings:%d", Id),
			"category",
			data.Category,
			"eid",
			data.Eid,
			"embedding",
			vectorBytes,
		}

		_, err = conn.Do("HMSET", ls...)
		if err != nil {
			logx.Error("redis hmset err:" + err.Error())
		}

		s.Mutex.Lock()
		s.PLog[data.Category].Success++
		s.Mutex.Unlock()
	}
}
