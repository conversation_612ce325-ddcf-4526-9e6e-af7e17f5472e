syntax = "v1"

info(
    title: "completion"
    author: "ligenhui"
    email: "<EMAIL>"
    version: "v3"
)

type (
    CompletionCreateReq {
        Text string `form:"text" validate:"required" v:"需要处理的文字"`
        Eid int64 `form:"eid" v:"源数据id"`
        Uid int64 `form:"uid" v:"用户"`
        Category string `form:"category" v:"所属分类"`
    }

    WxArticlesMappingReq {
        Eid int64 `form:"eid" validate:"min=1" v:"源数据id"`
    }
    WxArticlesMappingResp {
        LinkUrl string `json:"link_url"`
    }

    CompletionTextReq {
        Text string `form:"text" validate:"required" v:"需要处理的文字"`
    }
    CompletionTextResp {
        CompletionTextReq
    }

    CpywritingGenerationReq {
        Text string `json:"text" validate:"required" v:"酒款信息"`
        OpId string `json:"op_id" validate:"required" v:"操作人id"`
        OpName string `json:"op_name" validate:"required" v:"操作人名称"`
        ShortCode string `json:"short_code" validate:"required" v:"简码"`
    }
    CpywritingGenerationResp {
        Title string `json:"title"`
        Subtitle string `json:"subtitle"`
        Text string `json:"text"`
    }

    ForwardReq {
        Data interface{} `json:"data"`
    }
    ForwardResp {
        Result interface{} `json:"result"`
    }
    CommentReq {
        UseDs bool `json:"use_ds"`
        UseGpt bool `json:"use_gpt"`
        Prompt string `json:"prompt" validate:"required" v:"提示词"`
        Title string `json:"title" validate:"required" v:"标题"`
        temperature float32 `json:"temperature,default=0.7,optional" v:"温度值"`
        LatestComments string `json:"latest_comments,optional" v:"最近评论"`
    }
    CommentResp {
        DsContent string `json:"ds_content"`
        DsThink string `json:"ds_think"`
        GptContent string `json:"gpt_content"`
    }

    CommentStreamReq {
        UseDs bool `form:"use_ds,optional"`
        UseGpt bool `form:"use_gpt,optional"`
        Pid int64 `form:"pid"`
        Prompt string `form:"prompt" validate:"required" v:"提示词"`
        Title string `form:"title" validate:"required" v:"标题"`
        temperature float32 `form:"temperature,default=0.7,optional" v:"温度值"`
        WaitTime int64 `form:"wait_time,default=25,optional" v:"等待时间"`
    }

    MarkdownToHtmlReq {
        Info string `json:"info" validate:"required" v:"需转换的的md数据"`
    }
    MarkdownToHtmlResp {
        Info string `json:"info"`
    }
)

@server(
    middleware: Global,Event
    group: completion
    prefix: /embedding/v3/completion
)

service chatEmbedding{
    @handler Create
    get /create (CompletionCreateReq)

    @handler CommentStream
    get /commentStream (CommentStreamReq)
}

@server(
    middleware: Global,User
    group: completion
    prefix: /embedding/v3/completion
)

service chatEmbedding {
    @handler WxArticlesMapping
    get /wx_articles_mapping (WxArticlesMappingReq) returns (WxArticlesMappingResp)

    @handler Text
    get /text (CompletionTextReq) returns (CompletionTextResp)

    @handler CpywritingGeneration
    post /copywriting_generation (CpywritingGenerationReq) returns (CpywritingGenerationResp)

    @handler Forward
    post /forward (ForwardReq) returns (ForwardResp)

    @handler Comment
    post /comment (CommentReq) returns (CommentResp)

    @handler MarkdownToHtml
    post /markdownToHtml (MarkdownToHtmlReq) returns (MarkdownToHtmlResp)
}