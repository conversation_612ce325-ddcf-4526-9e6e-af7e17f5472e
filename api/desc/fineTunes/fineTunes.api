syntax = "v1"

info(
    title: "fine-tunes"
    author: "ligenhui"
    email: "<EMAIL>"
    version: "v3"
)

type (
    FineTunesMatchInstructReq {
        Text string `json:"text" validate:"required" v:"需要处理的文字"`
        Model string `json:"model,default=curie:ft-personal:vinehoo-os-2023-04-03-01-22-14" v:"模型"`
    }

    FineTunesMatchInstructResp {
        IsInstruct int64 `json:"is_instruct"`
        Result interface{} `json:"result"`
    }

    FineTunesInstructs {
        Instructs []FineTunesInstructInfo `json:"its"`
    }
    FineTunesInstructInfo {
        E string `json:"e"`
        Path string `json:"p"`
        Data interface{} `json:"d"`
        Module string `json:"m"`
        Genre string `json:"t"`
        Analysis string `json:"a"`
    }
)

@server(
    middleware: Global
    group: fineTunes
    prefix: /embedding/v3/fineTunes
)

service chatEmbedding {
    @handler MatchInstruct
    post /matchInstruct (FineTunesMatchInstructReq) returns (FineTunesMatchInstructResp)
}