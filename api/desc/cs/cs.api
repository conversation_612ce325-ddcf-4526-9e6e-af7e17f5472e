syntax = "v1"

info(
    title: "cs"
    author: "ligenhui"
    email: "<EMAIL>"
    version: "v3"
)

type (
    CsCreateReq {
        Text string `json:"text" validate:"required" v:"需要处理的文字"`
        Title string `json:"title" validate:"required" v:"标题"`
    }

    CsUpdateReq {
        id string `json:"id" validate:"required" v:"id"`
        CsCreateReq
    }

    CsDeleteReq {
        id string `json:"id" validate:"required" v:"id"`
    }

    CsQueryReq {
        Text string `json:"text" validate:"required" v:"需要检索的文字"`
    }

    CsQueryResp {
        List []QueryInfo `json:"list"`
    }
    QueryInfo {
        Id string `json:"id"`
        Title string `json:"title"`
        Content string `json:"content"`
        Score float64 `json:"score"`
    }
)

@server(
    middleware: Global
    group: cs
    prefix: /embedding/v3/cs
)

service chatEmbedding {
    @handler Create //创建
    post /create (CsCreateReq)

    @handler Update //修改
    post /update (CsUpdateReq)

    @handler Delete //删除
    post /delete (CsDeleteReq)

    @handler Query //检索
    post /query (CsQueryReq) returns (CsQueryResp)
}