syntax = "v1"

info(
    title: "embedding"
    author: "ligenhui"
    email: "<EMAIL>"
    version: "v3"
)

type (
    EmbeddingCreateReq {
        Text string `json:"text" validate:"required" v:"需要处理的文字"`
        Eid int64 `json:"eid" v:"源数据id"`
        Category string `json:"category" v:"所属分类"`
    }

    EmbeddingProcessResp {
        List []*EmbeddingProcessPLog `json:"list"`
    }
    EmbeddingProcessPLog {
        Name string `json:"name"`
        Total int64 `json:"total"`
        Success int64 `json:"success"`
        Fail int64 `json:"fail"`
        FailEid []int64 `json:"failEid"`
    }

    EmbeddingSuggestionReq {
        Eid int64 `form:"eid,optional" v:"源数据id"`
        Category string `form:"category,optional" v:"所属分类"`
        Query string `form:"query,optional" v:"搜索内容"`
        Genre string `form:"genre" v:"所属分类" validate:"required"`
    }

    EmbeddingSuggestionResp {
        List []*EmbeddingEmbeddingSuggestion `json:"list"`
    }
    EmbeddingEmbeddingSuggestion {
        Eid int64 `json:"eid"`
        Category string `json:"category"`
        Content string `json:"content"`
    }

    EmbeddingTextReq {
        Model int64 `json:"model,default=1"`//2 AdaEmbeddingV3Large
        Text string `json:"text" validate:"required" v:"需要处理的文字"`
    }

    EmbeddingTextResp {
        Embedding []float32 `json:"embedding"`
    }
)

@server(
    middleware: Global,User
    group: embedding
    prefix: /embedding/v3/embedding
)

service chatEmbedding {
    @handler Create //创建
    post /create (EmbeddingCreateReq)

    @handler ProcessInfo //处理详情
    get /processInfo returns (EmbeddingProcessResp)

    @handler Suggestion //获取相识推荐
    get /suggestion (EmbeddingSuggestionReq) returns (EmbeddingSuggestionResp)

    @handler Text
    post /text (EmbeddingTextReq) returns (EmbeddingTextResp)
}