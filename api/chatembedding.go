package main

import (
	cf "engine/common/config"
	_ "engine/common/logger"
	"fmt"
	"net/http"

	"engine/api/internal/config"
	"engine/api/internal/handler"
	"engine/api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func main() {

	c := config.NewConfig()
	cf.InitApiConfig(c, "go-chat-embedding", "vinehoo.conf", 0)

	server := rest.MustNewServer(c.RestConf, rest.WithCustomCors(func(header http.Header) {
		header.Set("Access-Control-Allow-Headers", "Content-Type, Origin, X-CSRF-Token, Authorization, AccessToken, Token, Range, vinehoo-uid, dingtalk-dept-id, vinehoo-client, vinehoo-client-version, access-token, nouncestr, dingtalk-uid, t, Sign")
		header.Set("Access-Control-Allow-Origin", "*")
		header.Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	}, func(w http.ResponseWriter) {
		w.<PERSON><PERSON>ead<PERSON>(http.StatusOK)
	}))
	defer server.Stop()

	ctx := svc.NewServiceContext(*c)
	handler.RegisterHandlers(server, ctx)

	fmt.Printf("Starting server at %s:%d...\n", c.Host, c.Port)
	server.Start()
}
