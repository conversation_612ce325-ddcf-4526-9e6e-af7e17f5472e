package nuonuo

const (
	// ServerUrl 正式环境
	ServerUrl = "https://sdk.nuonuo.com/open/v1/services"
	// SandboxUrl 沙盒环境
	SandboxUrl = "https://sandbox.nuonuocs.cn/open/v1/services"

	// MethodCreate 开具发票
	MethodCreate = "nuonuo.ElectronInvoice.requestBillingNew"
	// MethodClose 发票作废
	MethodClose = "nuonuo.electronInvoice.invoiceCancellation"
	// MethodQuery 开票结果查询
	MethodQuery = "nuonuo.ElectronInvoice.queryInvoiceResult"

	CodeOk = "E0000"
)

type CommonRsp struct {
	Code     string `json:"code"`
	Describe string `json:"describe"`
}

type CreateRsp struct {
	CommonRsp
	Result *CreateResult `json:"result,optional"`
}

type QueryRsp struct {
	CommonRsp
	Result []*QueryResult `json:"result,optional"`
}

type AbandonmentRsp struct {
	CommonRsp
	Result []*AbandonmentResult `json:"result,optional"`
}

type CreateResult struct {
	InvoiceSerialNum string `json:"invoiceSerialNum"`
}

type AbandonmentResult struct {
	InvoiceId string `json:"invoiceId"`
}

type QueryResult struct {
	Address          string `json:"address"`
	BankAccount      string `json:"bankAccount"`
	Checker          string `json:"checker"`
	Clerk            string `json:"clerk"`
	ClerkId          string `json:"clerkId"`
	CreateTime       int64  `json:"createTime"`
	DeptId           string `json:"deptId"`
	ExtensionNumber  string `json:"extensionNumber"`
	ImgUrls          string `json:"imgUrls"`
	InvoiceDate      int64  `json:"invoiceDate"`
	InvoiceType      string `json:"invoiceType"`
	ListFlag         string `json:"listFlag"`
	ListName         string `json:"listName"`
	NotifyEmail      string `json:"notifyEmail"`
	OldInvoiceCode   string `json:"oldInvoiceCode"`
	OldInvoiceNo     string `json:"oldInvoiceNo"`
	OrderAmount      string `json:"orderAmount"`
	Payee            string `json:"payee"`
	Phone            string `json:"phone"`
	ProductOilFlag   int64  `json:"productOilFlag"`
	ProxyInvoiceFlag string `json:"proxyInvoiceFlag"`
	Remark           string `json:"remark"`
	RequestSrc       string `json:"requestSrc"`
	SName            string `json:"sName"`
	SalerAccount     string `json:"salerAccount"`
	SalerAddress     string `json:"salerAddress"`
	SalerTaxNum      string `json:"salerTaxNum"`
	SalerTel         string `json:"salerTel"`
	SpecificFactor   int64  `json:"specificFactor"`
	TelrminalNumber  string `json:"telrminalNumber"`
	UpdateTime       int64  `json:"updateTime"`
	SerialNo         string `json:"serialNo"`
	OrderNo          string `json:"orderNo"`
	Status           string `json:"status"`
	StatusMsg        string `json:"statusMsg"`
	FailCause        string `json:"failCause"`
	PdfUrl           string `json:"pdfUrl"`
	PictureUrl       string `json:"pictureUrl"`
	InvoiceTime      int64  `json:"invoiceTime"`
	InvoiceCode      string `json:"invoiceCode"`
	InvoiceNo        string `json:"invoiceNo"`
	ExTaxAmount      string `json:"exTaxAmount"`
	TaxAmount        string `json:"taxAmount"`
	PayerName        string `json:"payerName"`
	PayerTaxNo       string `json:"payerTaxNo"`
	InvoiceKind      string `json:"invoiceKind"`
	CheckCode        string `json:"checkCode"`
	QrCode           string `json:"qrCode"`
	MachineCode      string `json:"machineCode"`
	CipherText       string `json:"cipher_text"`
}

type CallbackCreateNotify struct {
	Ct     int
	Url    string
	Genre  int //1电票回掉结果
	Orders []*CallbackCreateNotifyOrder
}

type CallbackCreateNotifyOrder struct {
	OrderNo string `json:"order_no"`
	Status  int64  `json:"status"`
}
