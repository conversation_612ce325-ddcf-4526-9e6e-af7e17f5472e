package nuonuo

import (
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"github.com/go-resty/resty/v2"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/stringx"
	"time"
)

type Client struct {
	Config Config
}

func New(config Config) *Client {
	return &Client{Config: config}
}

func Sign(appSecret string, appKey string, senid string, nonce string, timestamp string, body map[string]interface{}) (string, error) {
	b, err := json.Marshal(body)
	if err != nil {
		return "", err
	}
	signStr := fmt.Sprintf("a=services&l=v1&p=open&k=%s&i=%s&n=%s&t=%s&f=%s", appKey, senid, nonce, timestamp, string(b))
	mac := hmac.New(sha1.New, []byte(appSecret))
	mac.Write([]byte(signStr))
	hash := mac.Sum(nil)
	return base64.StdEncoding.EncodeToString(hash), nil
}

func GetHeader(sign string, token string, taxNum string, method string) map[string]string {
	return map[string]string{
		"X-Nuonuo-Sign": sign,
		"accessToken":   token,
		"userTax":       taxNum,
		"method":        method,
		"sdkVer":        "2.0.0",
	}
}

func SetHeaders(r *resty.Request, headers map[string]string) {
	for k, v := range headers {
		r.Header[k] = []string{v}
	}
}

func GetUrl(url string, senid string, nonce string, timestamp string, appKey string) string {
	return fmt.Sprintf("%s?senid=%s&nonce=%s&timestamp=%s&appkey=%s", url, senid, nonce, timestamp, appKey)
}

func (c *Client) Create(config InvoiceConfig, body map[string]interface{}) (*CreateResult, error) {
	timestamp := cast.ToString(time.Now().Unix())
	nonce := cast.ToString(time.Now().UnixNano())[8:16]
	senId := stringx.Randn(32)

	sign, err := Sign(config.AppSecret, config.AppKey, senId, nonce, timestamp, body)
	if err != nil {
		return nil, err
	}

	rsp := &CommonRsp{}
	cli := resty.New().R()
	SetHeaders(cli, GetHeader(sign, config.Token, config.NormalTax, MethodCreate))
	response, err := cli.SetBody(body).SetResult(rsp).Post(GetUrl(SandboxUrl, senId, nonce, timestamp, config.AppKey))
	if err != nil {
		return nil, err
	}

	if rsp.Code != CodeOk {
		return nil, fmt.Errorf(rsp.Describe)
	}

	createRsp := &CreateRsp{}

	err = json.Unmarshal(response.Body(), createRsp)
	if err != nil {
		return nil, err
	}
	return createRsp.Result, err
}

func (c *Client) Query(config InvoiceConfig, body map[string]interface{}) ([]*QueryResult, error) {
	timestamp := cast.ToString(time.Now().Unix())
	nonce := cast.ToString(time.Now().UnixNano())[8:16]
	senId := stringx.Randn(32)

	sign, err := Sign(config.AppSecret, config.AppKey, senId, nonce, timestamp, body)
	if err != nil {
		return nil, err
	}

	rsp := &CommonRsp{}
	cli := resty.New().R()
	SetHeaders(cli, GetHeader(sign, config.Token, config.NormalTax, MethodQuery))
	response, err := cli.SetBody(body).SetResult(rsp).Post(GetUrl(SandboxUrl, senId, nonce, timestamp, config.AppKey))
	if err != nil {
		return nil, err
	}

	if rsp.Code != CodeOk {
		return nil, fmt.Errorf(rsp.Describe)
	}

	queryRsp := &QueryRsp{}

	err = json.Unmarshal(response.Body(), queryRsp)
	if err != nil {
		return nil, err
	}
	return queryRsp.Result, err
}

func (c *Client) Abandonment(config InvoiceConfig, body map[string]interface{}) ([]*AbandonmentResult, error) {
	timestamp := cast.ToString(time.Now().Unix())
	nonce := cast.ToString(time.Now().UnixNano())[8:16]
	senId := stringx.Randn(32)

	sign, err := Sign(config.AppSecret, config.AppKey, senId, nonce, timestamp, body)
	if err != nil {
		return nil, err
	}

	rsp := &CommonRsp{}
	cli := resty.New().R()
	SetHeaders(cli, GetHeader(sign, config.Token, config.NormalTax, MethodClose))
	response, err := cli.SetBody(body).SetResult(rsp).Post(GetUrl(SandboxUrl, senId, nonce, timestamp, config.AppKey))
	if err != nil {
		return nil, err
	}

	fmt.Println(string(response.Body()))
	if rsp.Code != CodeOk {
		return nil, fmt.Errorf(rsp.Describe)
	}

	abandonmentRsp := &AbandonmentRsp{}

	err = json.Unmarshal(response.Body(), abandonmentRsp)
	if err != nil {
		return nil, err
	}
	return abandonmentRsp.Result, err
}
