package xredis

import (
	"engine/common/xerr"
	"errors"
	"fmt"
	"github.com/gomodule/redigo/redis"
	"github.com/zeromicro/go-zero/core/logx"
	"time"
)

type XFun func(conn redis.Conn) (interface{}, error)

const (
	MaxLockTime = 2000
	ScriptLock  = `
	if redis.call("get", KEYS[1]) == ARGV[1] then
		-- 返回0，代表key不在
		return redis.call("del", KEYS[1])
	else
		-- key在，但是值不对 代表其他地方已经使用该key
		return 0
	end
`
)

var LockErr = errors.New("lock fail")

func Do(pool *redis.Pool, fun XFun) (interface{}, error) {
	c := pool.Get()
	defer func() {
		err := c.Close()
		if err != nil {
			logx.Error(fmt.Sprintf("redigo 关闭链接失败 err%s", err.Error()))
		}
	}()

	result, e := fun(c)
	if e != nil {
		if _, ok := e.(*xerr.CodeError); !ok {
			logx.Error("XRedis Do error %s", e.Error())
		}
	}

	return result, e
}

func Lock(pool *redis.Pool, key string, expire int, fun XFun) (interface{}, error) {
	return Do(pool, func(conn redis.Conn) (interface{}, error) {
		var isLock bool
		t := time.Now().Unix()
		defer func() {
			//捕获全局异常
			if err := recover(); err != nil {
				logx.Error("XRedis lock error %s", err)
			}

			if isLock {
				//加锁成功后才允许解锁 如果锁已经被其他人使用 lua脚本里面会自动跳过
				lua := redis.NewScript(1, ScriptLock)
				_, e := redis.Int(lua.Do(conn, key, t))

				if e != nil {
					logx.Error("XRedis lock DEL key err %s", e.Error())
				}
			}
		}()

		timer := time.NewTimer(time.Duration(expire) * time.Millisecond)
		_, err := redis.String(conn.Do("SET", key, t, "NX", "PX", expire))
		if err != nil {
			//加锁失败 直接退出
			return nil, LockErr
		}

		isLock = true

		//超时管理
		go func() {
			<-timer.C

			//超时后不用在释放锁 并且链接不可使用了
			isLock = false
			_ = conn.Close()
			fmt.Println("超时关闭链接")
		}()

		return fun(conn)
	})
}
