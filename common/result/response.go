package result

const (
	SSE_ID_MESSAGE      = 1
	SSE_ID_ERR          = 2
	SSE_ID_CLOSE        = 3
	SSE_ID_DEFAULT      = 4
	SSE_ID_PARAM        = 5
	SSE_EVENT_MESSAGE   = "message"
	SSE_EVENT_ERR       = "err"
	SSE_EVENT_CLOSE     = "close"
	SSE_EVENT_PARAM_ERR = "param_err"
)

type ResponseSuccess struct {
	Code uint32      `json:"error_code"`
	Msg  string      `json:"error_msg"`
	Data interface{} `json:"data"`
}
type NullJson struct{}

func Success(data interface{}) *ResponseSuccess {
	return &ResponseSuccess{0, "OK", data}
}

type ResponseError struct {
	Code uint32 `json:"error_code"`
	Msg  string `json:"error_msg"`
}

func Error(errCode uint32, errMsg string) *ResponseError {
	return &ResponseError{errCode, errMsg}
}
