package result

import (
	"engine/common/logger"
	"engine/common/xerr"
	"fmt"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/rest/httpx"
	"net/http"
)

func HttpResult(r *http.Request, w http.ResponseWriter, resp interface{}, err error) {
	if err == nil {
		r := Success(resp)
		httpx.WriteJson(w, http.StatusOK, r)
	} else {
		code := http.StatusInternalServerError
		errCode := xerr.ServerCommonError
		errMsg := "服务器开小差啦，稍后再试"

		causeErr := errors.Cause(err)
		if e, ok := causeErr.(*xerr.CodeError); ok {
			errCode = e.GetErrCode()
			errMsg = e.GetErrMsg()
		}

		if errCode != xerr.DbError && errCode != xerr.ServerCommonError {
			code = http.StatusOK
		}
		logger.E("API-ERR", err)

		httpx.WriteJson(w, code, Error(errCode, errMsg))
	}
}

func ParamErrorResult(r *http.Request, w http.ResponseWriter, err error) {
	errMsg := fmt.Sprintf("%s ,%s", xerr.MapErrMsg(xerr.RequestParamError), err.Error())
	httpx.WriteJson(w, http.StatusOK, Error(xerr.RequestParamError, errMsg))
}

func SSEResult(w http.ResponseWriter, f http.Flusher, id int, event string, data interface{}) {
	_, _ = fmt.Fprintf(w, "id: %d\nevent: %s\ndata: %s\n\n", id, event, data)
	f.Flush()
}

func SSEEvent(w http.ResponseWriter, f http.Flusher, event string, data string) {
	var id int
	switch event {
	case SSE_EVENT_MESSAGE:
		id = SSE_ID_MESSAGE
		break
	case SSE_EVENT_ERR:
		id = SSE_ID_ERR
		break
	case SSE_EVENT_CLOSE:
		id = SSE_ID_CLOSE
		break
	default:
		id = SSE_ID_DEFAULT
	}
	SSEResult(w, f, id, event, data)
}

func SSEError(w http.ResponseWriter, f http.Flusher, data string) {
	SSEResult(w, f, SSE_ID_ERR, SSE_EVENT_ERR, data)
}

func SSE_PARAM_ERROR(w http.ResponseWriter, f http.Flusher, data string) {
	SSEResult(w, f, SSE_ID_PARAM, SSE_EVENT_PARAM_ERR, data)
}

func SSESuccess(w http.ResponseWriter, f http.Flusher, data string) {
	SSEResult(w, f, SSE_ID_MESSAGE, SSE_EVENT_MESSAGE, data)
}
