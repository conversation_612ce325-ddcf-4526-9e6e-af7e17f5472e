package getui

import (
	"crypto/sha256"
	"encoding/hex"
	"engine/common/xerr"
	"fmt"
	"github.com/go-resty/resty/v2"
	"github.com/spf13/cast"
	"time"
)

type sign struct {
	Sign      string `json:"sign"`
	Timestamp string `json:"timestamp"`
}

type auth struct {
	Token      string
	ExpireTime int64
}

type Getui struct {
	Config *Config
	Auth   *auth
}

func NewGetui(config Config) *Getui {
	return &Getui{
		Config: &config,
		Auth:   &auth{},
	}
}

func (g *Getui) GetToken() (string, error) {
	//提前30秒过期 防止后面使用失效
	if g.Auth.ExpireTime < (time.Now().Unix() + 30) {
		client := resty.New()
		response := &CommonResp{}
		sg := g.getSign()
		_, err := client.R().SetBody(map[string]string{"sign": sg.Sign, "timestamp": sg.Timestamp, "appkey": g.Config.AppKey}).
			SetResult(response).Post(BaseUrl + g.Config.AppId + AuthPath)
		if err != nil {
			return "", xerr.NewErrMsg(err.Error())
		}
		if response.Code != 0 {
			return "", xerr.NewErrMsg(response.Msg)
		}

		data := cast.ToStringMapString(response.Data)
		if expireTime, ok := data["expire_time"]; !ok || len(expireTime) != 13 {
			return "", xerr.NewErrMsg("token有效期获取失败")
		} else {
			g.Auth.ExpireTime = cast.ToInt64(expireTime[:len(expireTime)-3])
		}
		if token, ok := data["token"]; !ok || token == "" {
			return "", xerr.NewErrMsg("token值获取失败")
		} else {
			g.Auth.Token = token
		}
	}
	return g.Auth.Token, nil
}

func (g *Getui) getSign() *sign {
	timestamp := time.Now().UnixNano() / 1e6
	signStr := fmt.Sprintf("%s%d%s", g.Config.AppKey, timestamp, g.Config.MasterSecret)
	m := sha256.New()
	m.Write([]byte(signStr))
	sg := hex.EncodeToString(m.Sum(nil))
	return &sign{
		Sign:      sg,
		Timestamp: cast.ToString(timestamp),
	}
}

// CreateTransmissionMessage 创建透传消息
func (g *Getui) CreateTransmissionMessage(transmissionMessage TransmissionMessage) (string, error) {
	token, err := g.GetToken()
	if err != nil {
		return "", err
	}

	client := resty.New()
	response := &CommonResp{}

	r, err := client.R().SetHeader("token", token).SetBody(transmissionMessage).
		SetResult(response).Post(BaseUrl + g.Config.AppId + CreateMessage)

	if r.StatusCode() != 200 {
		return "", xerr.NewErrMsg("创建消息异常:" + r.String())
	}
	if err != nil {
		return "", xerr.NewErrMsg(err.Error())
	}

	if response.Code != 0 {
		return "", xerr.NewErrMsg(response.Msg)
	}

	data := cast.ToStringMapString(response.Data)
	if taskId, ok := data["taskid"]; !ok {
		return "", xerr.NewErrMsg("taskid获取失败")
	} else {
		return taskId, nil
	}
}

// AliasBatchPush 别名批量推送
func (g *Getui) AliasBatchPush(aliasBatch AliasBatch) (string, error) {
	if len(aliasBatch.Audience.Alias) > 1000 {
		return "", xerr.NewErrMsg("单次推送人数不能超过1000")
	}

	token, err := g.GetToken()
	if err != nil {
		return "", err
	}

	client := resty.New()
	response := &CommonResp{}

	r, err := client.R().SetHeader("token", token).SetBody(aliasBatch).
		SetResult(response).Post(BaseUrl + g.Config.AppId + AliasBatchPush)

	if r.StatusCode() != 200 {
		return "", xerr.NewErrMsg("推送消息异常:" + r.String())
	}

	if err != nil {
		return "", xerr.NewErrMsg(err.Error())
	}

	if response.Code != 0 {
		return "", xerr.NewErrMsg(response.Msg)
	}

	return r.String(), nil
}
