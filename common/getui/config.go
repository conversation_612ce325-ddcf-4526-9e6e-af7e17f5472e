package getui

const (
	BaseUrl = "https://restapi.getui.com/v2/"
	// AuthPath 鉴权
	AuthPath = "/auth"

	// CreateMessage 创建消息
	CreateMessage = "/push/list/message"

	// AliasBatchPush 别名批量推送
	AliasBatchPush = "/push/list/alias"
)

type Config struct {
	AppKey       string
	MasterSecret string
	AppId        string
}

type CommonResp struct {
	Code int64       `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}
