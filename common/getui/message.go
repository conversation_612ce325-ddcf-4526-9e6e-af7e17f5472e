package getui

type TransmissionMessage struct {
	PushMessage *Transmission   `json:"push_message"`
	PushChannel *ChannelMessage `json:"push_channel"`
}

type Transmission struct {
	Transmission string `json:"transmission"`
}

type ChannelMessage struct {
	Ios     *ChannelIos     `json:"ios"`
	Android *ChannelAndroid `json:"android"`
}

type ChannelAndroid struct {
	Ups *ChannelAndroidUps `json:"ups"`
}

type ChannelAndroidUps struct {
	Transmission string `json:"transmission"`
}

type ChannelIos struct {
	Type      string         `json:"type"`
	Aps       *ChannelIosAps `json:"aps"`
	AutoBadge string         `json:"auto_badge"`
	Payload   string         `json:"payload"`
}

type ChannelIosAps struct {
	Alert *ChannelIosAlert `json:"alert"`
}

type ChannelIosAlert struct {
	Title string
	Body  string
}
