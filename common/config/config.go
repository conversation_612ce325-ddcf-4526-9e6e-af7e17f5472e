package config

import (
	"fmt"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/rest"
)

type ApiConfig struct {
	rest.RestConf
	Mysql struct {
		DataSource string
	}
	Cache        cache.CacheConf
	Redis        redis.RedisConf
	ChatGptToken string
}

func InitApiConfig(ayn *ApiConfig, dataId, group string, operType int) {
	/*var data string
	defer func() {
		err := recover()
		if err != nil {
			logger.E(fmt.Sprintf("%s config init Error", dataId))
			panic(err)
		}
	}()

	data, bs := nacos.GetString(dataId, group, func(data *string, err error) {
		if err == nil {
			loadApiConfig(operType, *data, dataId, ayn)
		}
	})
	fmt.Println(bs)
	if data == "" {
		panic(fmt.Errorf("%s config is empty", dataId))
	}*/
	data := `Port: 8888
Name: go-chat-embedding

Timeout: 0

Log:
  ServiceName: go-chat-embedding
  Mode: file
  Level: info
Middlewares:
  Shedding: false


Mysql:
  DataSource: wy_chat_gpt:8SzP5#tSyFSaq7g4fVCSeC3J@tcp(rm-8vb8nfr498cxlyhduwo.mysql.zhangbei.rds.aliyuncs.com:3306)/vh_embeddings?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai

Cache:
  - Host: r-8vbyf9qn03iwmy7w5npd.redis.zhangbei.rds.aliyuncs.com:6379
    Pass: NRDSYa5e6EWZuJ3d

Redis:
  Host: *************:6380
  Type: node
  Pass: vinehoo666

ChatGptToken: ***************************************************`
	loadApiConfig(operType, data, dataId, ayn)
}

func loadApiConfig(operType int, data, dataId string, ayn *ApiConfig) {
	if operType == 0 {
		err := conf.LoadFromYamlBytes([]byte(data), ayn)
		if err != nil {
			panic(fmt.Errorf("%s config Yaml Error %s", dataId, err))
		}
	}
}
