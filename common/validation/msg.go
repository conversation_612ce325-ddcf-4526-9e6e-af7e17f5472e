package validation

import (
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	"reflect"
)

//registerTagName 注册自定义字段名描述
func (v *Verify) registerTagName() {
	v.validate.RegisterTagNameFunc(func(fld reflect.StructField) string {
		return fld.Tag.Get("v")
	})
}

//注册错误描述信息
func (v *Verify) registerErrMsg() {
	v.registerErrMsgFunc("required", "请输入{0}")
	v.registerErrMsgFunc("phone", "请输入有效{0}")
	v.registerErrMsgFunc("pwd", "{0}只能是任意可见字符，长度在6~18之间")
	v.registerErrMsgFunc("idCard", "请输入有效{0}")
	v.registerErrMsgFunc("bank-card", "请输入有效{0}")
	v.registerErrMsgFunc("pin", "{0}只能是6位数字")
	v.registerErrMsgFunc("code", "{0}只能是数字和字母组合6位")
	v.registerErrMsgFunc("email", "请输入有效{0}")
	v.registerErrMsgFunc("img", "{0}只能是png|jpg|jpeg|gif")
	v.registerErrMsgFunc("mp4", "{0}只能是mp4")
	v.registerErrMsgFunc("timeLt", "{0}不能超过当前时间")
}

//registerErrMsgFunc 注册错误描述信息处理
func (v *Verify) registerErrMsgFunc(key, msg string) {
	_ = v.validate.RegisterTranslation(key, v.trans, func(ut ut.Translator) error {
		return ut.Add(key, msg, true)
	}, func(ut ut.Translator, fe validator.FieldError) string {
		t, _ := ut.T(key, fe.Field())
		return t
	})
}
