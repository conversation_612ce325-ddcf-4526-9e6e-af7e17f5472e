package model

import "github.com/zeromicro/go-zero/core/stores/sqlx"

var _ VhQaLogModel = (*customVhQaLogModel)(nil)

type (
	// VhQaLogModel is an interface to be customized, add more methods here,
	// and implement the added methods in customVhQaLogModel.
	VhQaLogModel interface {
		vhQaLogModel
	}

	customVhQaLogModel struct {
		*defaultVhQaLogModel
	}
)

// NewVhQaLogModel returns a model for the database table.
func NewVhQaLogModel(conn sqlx.SqlConn) VhQaLogModel {
	return &customVhQaLogModel{
		defaultVhQaLogModel: newVhQaLogModel(conn),
	}
}
