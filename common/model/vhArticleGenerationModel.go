package model

import "github.com/zeromicro/go-zero/core/stores/sqlx"

var _ VhArticleGenerationModel = (*customVhArticleGenerationModel)(nil)

type (
	// VhArticleGenerationModel is an interface to be customized, add more methods here,
	// and implement the added methods in customVhArticleGenerationModel.
	VhArticleGenerationModel interface {
		vhArticleGenerationModel
	}

	customVhArticleGenerationModel struct {
		*defaultVhArticleGenerationModel
	}
)

// NewVhArticleGenerationModel returns a model for the database table.
func NewVhArticleGenerationModel(conn sqlx.SqlConn) VhArticleGenerationModel {
	return &customVhArticleGenerationModel{
		defaultVhArticleGenerationModel: newVhArticleGenerationModel(conn),
	}
}
