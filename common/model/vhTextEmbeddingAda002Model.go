package model

import (
	"context"
	"fmt"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var _ VhTextEmbeddingAda002Model = (*customVhTextEmbeddingAda002Model)(nil)

type (
	VhTextEmbeddingAda002ByEidCategoryContent struct {
		Eid      int64  `db:"eid"`      // 资料自身ID，比如商品详情的eid就是period_id
		Category string `db:"category"` // 分类
		Content  string `db:"content"`  // 原始数据
	}

	// VhTextEmbeddingAda002Model is an interface to be customized, add more methods here,
	// and implement the added methods in customVhTextEmbeddingAda002Model.
	VhTextEmbeddingAda002Model interface {
		vhTextEmbeddingAda002Model
		FindOneByEidCategory(ctx context.Context, eid int64, category string) (*VhTextEmbeddingAda002, error)
	}

	customVhTextEmbeddingAda002Model struct {
		*defaultVhTextEmbeddingAda002Model
	}
)

// NewVhTextEmbeddingAda002Model returns a model for the database table.
func NewVhTextEmbeddingAda002Model(conn sqlx.SqlConn) VhTextEmbeddingAda002Model {
	return &customVhTextEmbeddingAda002Model{
		defaultVhTextEmbeddingAda002Model: newVhTextEmbeddingAda002Model(conn),
	}
}

func (m *defaultVhTextEmbeddingAda002Model) FindOneByEidCategory(ctx context.Context, eid int64, category string) (*VhTextEmbeddingAda002, error) {
	query := fmt.Sprintf("select %s from %s where `eid` = ? and `category` = ? limit 1", vhTextEmbeddingAda002Rows, m.table)
	var resp VhTextEmbeddingAda002
	err := m.conn.QueryRowCtx(ctx, &resp, query, eid, category)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}
