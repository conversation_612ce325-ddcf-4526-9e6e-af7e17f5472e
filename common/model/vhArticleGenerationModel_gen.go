// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/Masterminds/squirrel"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	vhArticleGenerationFieldNames          = builder.RawFieldNames(&VhArticleGeneration{})
	vhArticleGenerationRows                = strings.Join(vhArticleGenerationFieldNames, ",")
	vhArticleGenerationRowsExpectAutoSet   = strings.Join(stringx.Remove(vhArticleGenerationFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), ",")
	vhArticleGenerationRowsWithPlaceHolder = strings.Join(stringx.Remove(vhArticleGenerationFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), "=?,") + "=?"
)

type (
	vhArticleGenerationModel interface {
		Insert(ctx context.Context, data *VhArticleGeneration) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*VhArticleGeneration, error)
		Update(ctx context.Context, data *VhArticleGeneration) error
		TableName() string
		RowBuilder() squirrel.SelectBuilder
		FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error)
		Delete(ctx context.Context, id int64) error
	}

	defaultVhArticleGenerationModel struct {
		conn  sqlx.SqlConn
		table string
	}

	VhArticleGeneration struct {
		Id         int64     `db:"id"`
		OpId       string    `db:"op_id"`       // 操作人id
		OpName     string    `db:"op_name"`     // 操作人名称
		ShortCode  string    `db:"short_code"`  // 简码
		Param      string    `db:"param"`       // 提交参数
		Result     string    `db:"result"`      // 相应结果
		CreateTime time.Time `db:"create_time"` // 创建时间
	}
)

func newVhArticleGenerationModel(conn sqlx.SqlConn) *defaultVhArticleGenerationModel {
	return &defaultVhArticleGenerationModel{
		conn:  conn,
		table: "`vh_article_generation`",
	}
}

func (m *defaultVhArticleGenerationModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultVhArticleGenerationModel) FindOne(ctx context.Context, id int64) (*VhArticleGeneration, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", vhArticleGenerationRows, m.table)
	var resp VhArticleGeneration
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhArticleGenerationModel) Insert(ctx context.Context, data *VhArticleGeneration) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?)", m.table, vhArticleGenerationRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.OpId, data.OpName, data.ShortCode, data.Param, data.Result)
	return ret, err
}

func (m *defaultVhArticleGenerationModel) Update(ctx context.Context, data *VhArticleGeneration) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, vhArticleGenerationRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.OpId, data.OpName, data.ShortCode, data.Param, data.Result, data.Id)
	return err
}

func (m *defaultVhArticleGenerationModel) RowBuilder() squirrel.SelectBuilder {
	return squirrel.Select(vhArticleGenerationRows).From(m.table)
}

func (m *defaultVhArticleGenerationModel) FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhArticleGenerationModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}
	var count int64

	err = m.conn.QueryRowCtx(ctx, &count, query, values...)

	switch err {
	case nil:
		return count, nil
	default:
		return 0, err
	}
}

func (m *defaultVhArticleGenerationModel) UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := m.conn.ExecCtx(ctx, query, values...)

	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhArticleGenerationModel) TableName() string {
	return m.table
}
