// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/Masterminds/squirrel"
	"strings"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	vhWxArticlesMappingFieldNames          = builder.RawFieldNames(&VhWxArticlesMapping{})
	vhWxArticlesMappingRows                = strings.Join(vhWxArticlesMappingFieldNames, ",")
	vhWxArticlesMappingRowsExpectAutoSet   = strings.Join(stringx.Remove(vhWxArticlesMappingFieldNames, "`create_time`", "`update_time`", "`create_at`", "`update_at`"), ",")
	vhWxArticlesMappingRowsWithPlaceHolder = strings.Join(stringx.Remove(vhWxArticlesMappingFieldNames, "`eid`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), "=?,") + "=?"
)

type (
	vhWxArticlesMappingModel interface {
		Insert(ctx context.Context, data *VhWxArticlesMapping) (sql.Result, error)
		FindOne(ctx context.Context, eid int64) (*VhWxArticlesMapping, error)
		Update(ctx context.Context, data *VhWxArticlesMapping) error
		TableName() string
		RowBuilder() squirrel.SelectBuilder
		FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error)
		Delete(ctx context.Context, eid int64) error
	}

	defaultVhWxArticlesMappingModel struct {
		conn  sqlx.SqlConn
		table string
	}

	VhWxArticlesMapping struct {
		Eid     int64  `db:"eid"`
		LinkUrl string `db:"link_url"`
	}
)

func newVhWxArticlesMappingModel(conn sqlx.SqlConn) *defaultVhWxArticlesMappingModel {
	return &defaultVhWxArticlesMappingModel{
		conn:  conn,
		table: "`vh_wx_articles_mapping`",
	}
}

func (m *defaultVhWxArticlesMappingModel) Delete(ctx context.Context, eid int64) error {
	query := fmt.Sprintf("delete from %s where `eid` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, eid)
	return err
}

func (m *defaultVhWxArticlesMappingModel) FindOne(ctx context.Context, eid int64) (*VhWxArticlesMapping, error) {
	query := fmt.Sprintf("select %s from %s where `eid` = ? limit 1", vhWxArticlesMappingRows, m.table)
	var resp VhWxArticlesMapping
	err := m.conn.QueryRowCtx(ctx, &resp, query, eid)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhWxArticlesMappingModel) Insert(ctx context.Context, data *VhWxArticlesMapping) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?)", m.table, vhWxArticlesMappingRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Eid, data.LinkUrl)
	return ret, err
}

func (m *defaultVhWxArticlesMappingModel) Update(ctx context.Context, data *VhWxArticlesMapping) error {
	query := fmt.Sprintf("update %s set %s where `eid` = ?", m.table, vhWxArticlesMappingRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.LinkUrl, data.Eid)
	return err
}

func (m *defaultVhWxArticlesMappingModel) RowBuilder() squirrel.SelectBuilder {
	return squirrel.Select(vhWxArticlesMappingRows).From(m.table)
}

func (m *defaultVhWxArticlesMappingModel) FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhWxArticlesMappingModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}
	var count int64

	err = m.conn.QueryRowCtx(ctx, &count, query, values...)

	switch err {
	case nil:
		return count, nil
	default:
		return 0, err
	}
}

func (m *defaultVhWxArticlesMappingModel) UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := m.conn.ExecCtx(ctx, query, values...)

	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhWxArticlesMappingModel) TableName() string {
	return m.table
}
