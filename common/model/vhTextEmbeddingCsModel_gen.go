// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/Masterminds/squirrel"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	vhTextEmbeddingCsFieldNames          = builder.RawFieldNames(&VhTextEmbeddingCs{})
	vhTextEmbeddingCsRows                = strings.Join(vhTextEmbeddingCsFieldNames, ",")
	vhTextEmbeddingCsRowsExpectAutoSet   = strings.Join(stringx.Remove(vhTextEmbeddingCsFieldNames, "`create_time`", "`update_time`", "`create_at`", "`update_at`"), ",")
	vhTextEmbeddingCsRowsWithPlaceHolder = strings.Join(stringx.Remove(vhTextEmbeddingCsFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), "=?,") + "=?"
)

type (
	vhTextEmbeddingCsModel interface {
		Insert(ctx context.Context, data *VhTextEmbeddingCs) (sql.Result, error)
		FindOne(ctx context.Context, id string) (*VhTextEmbeddingCs, error)
		Update(ctx context.Context, data *VhTextEmbeddingCs) error
		TableName() string
		RowBuilder() squirrel.SelectBuilder
		FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error)
		Delete(ctx context.Context, id string) error
	}

	defaultVhTextEmbeddingCsModel struct {
		conn  sqlx.SqlConn
		table string
	}

	VhTextEmbeddingCs struct {
		Id         string    `db:"id"`         // id(uuid)
		Title      string    `db:"title"`      // 标题
		Content    string    `db:"content"`    // 原始内容
		Embeddings string    `db:"embeddings"` // 原始emb,可供后续使用
		CreateTime time.Time `db:"create_time"`
	}
)

func newVhTextEmbeddingCsModel(conn sqlx.SqlConn) *defaultVhTextEmbeddingCsModel {
	return &defaultVhTextEmbeddingCsModel{
		conn:  conn,
		table: "`vh_text_embedding_cs`",
	}
}

func (m *defaultVhTextEmbeddingCsModel) Delete(ctx context.Context, id string) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultVhTextEmbeddingCsModel) FindOne(ctx context.Context, id string) (*VhTextEmbeddingCs, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", vhTextEmbeddingCsRows, m.table)
	var resp VhTextEmbeddingCs
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhTextEmbeddingCsModel) Insert(ctx context.Context, data *VhTextEmbeddingCs) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?)", m.table, vhTextEmbeddingCsRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Id, data.Title, data.Content, data.Embeddings)
	return ret, err
}

func (m *defaultVhTextEmbeddingCsModel) Update(ctx context.Context, data *VhTextEmbeddingCs) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, vhTextEmbeddingCsRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Title, data.Content, data.Embeddings, data.Id)
	return err
}

func (m *defaultVhTextEmbeddingCsModel) RowBuilder() squirrel.SelectBuilder {
	return squirrel.Select(vhTextEmbeddingCsRows).From(m.table)
}

func (m *defaultVhTextEmbeddingCsModel) FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhTextEmbeddingCsModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}
	var count int64

	err = m.conn.QueryRowCtx(ctx, &count, query, values...)

	switch err {
	case nil:
		return count, nil
	default:
		return 0, err
	}
}

func (m *defaultVhTextEmbeddingCsModel) UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := m.conn.ExecCtx(ctx, query, values...)

	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhTextEmbeddingCsModel) TableName() string {
	return m.table
}
