package model

import "github.com/zeromicro/go-zero/core/stores/sqlx"

var _ VhWxArticlesMappingModel = (*customVhWxArticlesMappingModel)(nil)

type (
	// VhWxArticlesMappingModel is an interface to be customized, add more methods here,
	// and implement the added methods in customVhWxArticlesMappingModel.
	VhWxArticlesMappingModel interface {
		vhWxArticlesMappingModel
	}

	customVhWxArticlesMappingModel struct {
		*defaultVhWxArticlesMappingModel
	}
)

// NewVhWxArticlesMappingModel returns a model for the database table.
func NewVhWxArticlesMappingModel(conn sqlx.SqlConn) VhWxArticlesMappingModel {
	return &customVhWxArticlesMappingModel{
		defaultVhWxArticlesMappingModel: newVhWxArticlesMappingModel(conn),
	}
}
