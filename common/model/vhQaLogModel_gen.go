// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/Masterminds/squirrel"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	vhQaLogFieldNames          = builder.RawFieldNames(&VhQaLog{})
	vhQaLogRows                = strings.Join(vhQaLogFieldNames, ",")
	vhQaLogRowsExpectAutoSet   = strings.Join(stringx.Remove(vhQaLogFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), ",")
	vhQaLogRowsWithPlaceHolder = strings.Join(stringx.Remove(vhQaLogFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), "=?,") + "=?"
)

type (
	vhQaLogModel interface {
		Insert(ctx context.Context, data *VhQaLog) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*VhQaLog, error)
		Update(ctx context.Context, data *VhQaLog) error
		TableName() string
		RowBuilder() squirrel.SelectBuilder
		FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error)
		Delete(ctx context.Context, id int64) error
	}

	defaultVhQaLogModel struct {
		conn  sqlx.SqlConn
		table string
	}

	VhQaLog struct {
		Id          int64     `db:"id"`
		Eid         int64     `db:"eid"`          // 数据id
		Category    string    `db:"category"`     // 分类
		Uid         int64     `db:"uid"`          // 用户id
		ReqContent  string    `db:"req_content"`  // 搜索内容
		RespContent string    `db:"resp_content"` // 响应内容
		CreateTime  time.Time `db:"create_time"`
	}
)

func newVhQaLogModel(conn sqlx.SqlConn) *defaultVhQaLogModel {
	return &defaultVhQaLogModel{
		conn:  conn,
		table: "`vh_qa_log`",
	}
}

func (m *defaultVhQaLogModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultVhQaLogModel) FindOne(ctx context.Context, id int64) (*VhQaLog, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", vhQaLogRows, m.table)
	var resp VhQaLog
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhQaLogModel) Insert(ctx context.Context, data *VhQaLog) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?)", m.table, vhQaLogRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Eid, data.Category, data.Uid, data.ReqContent, data.RespContent)
	return ret, err
}

func (m *defaultVhQaLogModel) Update(ctx context.Context, data *VhQaLog) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, vhQaLogRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Eid, data.Category, data.Uid, data.ReqContent, data.RespContent, data.Id)
	return err
}

func (m *defaultVhQaLogModel) RowBuilder() squirrel.SelectBuilder {
	return squirrel.Select(vhQaLogRows).From(m.table)
}

func (m *defaultVhQaLogModel) FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhQaLogModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}
	var count int64

	err = m.conn.QueryRowCtx(ctx, &count, query, values...)

	switch err {
	case nil:
		return count, nil
	default:
		return 0, err
	}
}

func (m *defaultVhQaLogModel) UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := m.conn.ExecCtx(ctx, query, values...)

	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhQaLogModel) TableName() string {
	return m.table
}
