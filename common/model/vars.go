package model

import (
	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

type ProcessData struct {
	Text     string `json:"text"`
	Eid      int64  `json:"eid"`
	Category string `json:"category"`
}

var (
	ErrNotFound = sqlx.ErrNotFound
	Category    = map[string]string{
		"product_details":  "商品详情",
		"community":        "社区",
		"wine_knowledge":   "葡萄酒知识",
		"wine_news":        "酒闻",
		"wine_wx_articles": "公众号文章",
	}
	CategoryById = map[int64]string{
		1: "product_details",
		2: "community",
		3: "wine_knowledge",
		4: "wine_news",
		5: "wine_wx_articles",
	}
)

const (
	Delete   = 1
	NoDelete = 0
)

func CountBuilder(field string, table string) squirrel.SelectBuilder {
	return squirrel.Select("COUNT(" + field + ") as count").From(table)
}

// GetOffset 获取起始数据
func GetOffset(page, pageSize int64) uint64 {
	return uint64((page - 1) * pageSize)
}
