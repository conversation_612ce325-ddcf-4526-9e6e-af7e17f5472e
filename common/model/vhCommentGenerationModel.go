package model

import "github.com/zeromicro/go-zero/core/stores/sqlx"

var _ VhCommentGenerationModel = (*customVhCommentGenerationModel)(nil)

type (
	// VhCommentGenerationModel is an interface to be customized, add more methods here,
	// and implement the added methods in customVhCommentGenerationModel.
	VhCommentGenerationModel interface {
		vhCommentGenerationModel
	}

	customVhCommentGenerationModel struct {
		*defaultVhCommentGenerationModel
	}
)

// NewVhCommentGenerationModel returns a model for the database table.
func NewVhCommentGenerationModel(conn sqlx.SqlConn) VhCommentGenerationModel {
	return &customVhCommentGenerationModel{
		defaultVhCommentGenerationModel: newVhCommentGenerationModel(conn),
	}
}
