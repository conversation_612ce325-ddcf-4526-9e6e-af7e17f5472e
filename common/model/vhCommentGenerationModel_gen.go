// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/Masterminds/squirrel"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	vhCommentGenerationFieldNames          = builder.RawFieldNames(&VhCommentGeneration{})
	vhCommentGenerationRows                = strings.Join(vhCommentGenerationFieldNames, ",")
	vhCommentGenerationRowsExpectAutoSet   = strings.Join(stringx.Remove(vhCommentGenerationFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), ",")
	vhCommentGenerationRowsWithPlaceHolder = strings.Join(stringx.Remove(vhCommentGenerationFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), "=?,") + "=?"
)

type (
	vhCommentGenerationModel interface {
		Insert(ctx context.Context, data *VhCommentGeneration) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*VhCommentGeneration, error)
		Update(ctx context.Context, data *VhCommentGeneration) error
		TableName() string
		RowBuilder() squirrel.SelectBuilder
		FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error)
		Delete(ctx context.Context, id int64) error
	}

	defaultVhCommentGenerationModel struct {
		conn  sqlx.SqlConn
		table string
	}

	VhCommentGeneration struct {
		Id         int64     `db:"id"`
		Req        string    `db:"req"`  // 请求数据
		Resp       string    `db:"resp"` // 响应数据
		CreateTime time.Time `db:"create_time"`
	}
)

func newVhCommentGenerationModel(conn sqlx.SqlConn) *defaultVhCommentGenerationModel {
	return &defaultVhCommentGenerationModel{
		conn:  conn,
		table: "`vh_comment_generation`",
	}
}

func (m *defaultVhCommentGenerationModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultVhCommentGenerationModel) FindOne(ctx context.Context, id int64) (*VhCommentGeneration, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", vhCommentGenerationRows, m.table)
	var resp VhCommentGeneration
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhCommentGenerationModel) Insert(ctx context.Context, data *VhCommentGeneration) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?)", m.table, vhCommentGenerationRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Req, data.Resp)
	return ret, err
}

func (m *defaultVhCommentGenerationModel) Update(ctx context.Context, data *VhCommentGeneration) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, vhCommentGenerationRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Req, data.Resp, data.Id)
	return err
}

func (m *defaultVhCommentGenerationModel) RowBuilder() squirrel.SelectBuilder {
	return squirrel.Select(vhCommentGenerationRows).From(m.table)
}

func (m *defaultVhCommentGenerationModel) FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhCommentGenerationModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}
	var count int64

	err = m.conn.QueryRowCtx(ctx, &count, query, values...)

	switch err {
	case nil:
		return count, nil
	default:
		return 0, err
	}
}

func (m *defaultVhCommentGenerationModel) UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := m.conn.ExecCtx(ctx, query, values...)

	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhCommentGenerationModel) TableName() string {
	return m.table
}
