// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/Masterminds/squirrel"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	vhTextEmbeddingAda002FieldNames          = builder.RawFieldNames(&VhTextEmbeddingAda002{})
	vhTextEmbeddingAda002Rows                = strings.Join(vhTextEmbeddingAda002FieldNames, ",")
	vhTextEmbeddingAda002RowsExpectAutoSet   = strings.Join(stringx.Remove(vhTextEmbeddingAda002FieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), ",")
	vhTextEmbeddingAda002RowsWithPlaceHolder = strings.Join(stringx.Remove(vhTextEmbeddingAda002FieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), "=?,") + "=?"
)

type (
	vhTextEmbeddingAda002Model interface {
		Insert(ctx context.Context, data *VhTextEmbeddingAda002) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*VhTextEmbeddingAda002, error)
		Update(ctx context.Context, data *VhTextEmbeddingAda002) error
		TableName() string
		RowBuilder() squirrel.SelectBuilder
		FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error)
		Delete(ctx context.Context, id int64) error
	}

	defaultVhTextEmbeddingAda002Model struct {
		conn  sqlx.SqlConn
		table string
	}

	VhTextEmbeddingAda002 struct {
		Id         int64     `db:"id"`
		Eid        int64     `db:"eid"`        // 资料自身ID，比如商品详情的eid就是period_id
		Category   string    `db:"category"`   // 分类
		Embeddings string    `db:"embeddings"` // 向量值
		CreateTime time.Time `db:"create_time"`
		Content    string    `db:"content"` // 原始数据
	}
)

func newVhTextEmbeddingAda002Model(conn sqlx.SqlConn) *defaultVhTextEmbeddingAda002Model {
	return &defaultVhTextEmbeddingAda002Model{
		conn:  conn,
		table: "`vh_text_embedding_ada_002`",
	}
}

func (m *defaultVhTextEmbeddingAda002Model) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultVhTextEmbeddingAda002Model) FindOne(ctx context.Context, id int64) (*VhTextEmbeddingAda002, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", vhTextEmbeddingAda002Rows, m.table)
	var resp VhTextEmbeddingAda002
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhTextEmbeddingAda002Model) Insert(ctx context.Context, data *VhTextEmbeddingAda002) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?)", m.table, vhTextEmbeddingAda002RowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Eid, data.Category, data.Embeddings, data.Content)
	return ret, err
}

func (m *defaultVhTextEmbeddingAda002Model) Update(ctx context.Context, data *VhTextEmbeddingAda002) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, vhTextEmbeddingAda002RowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Eid, data.Category, data.Embeddings, data.Content, data.Id)
	return err
}

func (m *defaultVhTextEmbeddingAda002Model) RowBuilder() squirrel.SelectBuilder {
	return squirrel.Select(vhTextEmbeddingAda002Rows).From(m.table)
}

func (m *defaultVhTextEmbeddingAda002Model) FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhTextEmbeddingAda002Model) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}
	var count int64

	err = m.conn.QueryRowCtx(ctx, &count, query, values...)

	switch err {
	case nil:
		return count, nil
	default:
		return 0, err
	}
}

func (m *defaultVhTextEmbeddingAda002Model) UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := m.conn.ExecCtx(ctx, query, values...)

	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhTextEmbeddingAda002Model) TableName() string {
	return m.table
}
