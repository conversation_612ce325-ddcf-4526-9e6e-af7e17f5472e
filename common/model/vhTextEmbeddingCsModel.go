package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var _ VhTextEmbeddingCsModel = (*customVhTextEmbeddingCsModel)(nil)

type (
	// VhTextEmbeddingCsModel is an interface to be customized, add more methods here,
	// and implement the added methods in customVhTextEmbeddingCsModel.
	VhTextEmbeddingCsModel interface {
		vhTextEmbeddingCsModel
		InsertTx(ctx context.Context, tx *sql.Tx, data *VhTextEmbeddingCs) (sql.Result, error)
		UpdateTx(ctx context.Context, tx *sql.Tx, data *VhTextEmbeddingCs) error
		DeleteTx(ctx context.Context, tx *sql.Tx, id string) error
	}

	customVhTextEmbeddingCsModel struct {
		*defaultVhTextEmbeddingCsModel
	}
)

// NewVhTextEmbeddingCsModel returns a model for the database table.
func NewVhTextEmbeddingCsModel(conn sqlx.SqlConn) VhTextEmbeddingCsModel {
	return &customVhTextEmbeddingCsModel{
		defaultVhTextEmbeddingCsModel: newVhTextEmbeddingCsModel(conn),
	}
}

func (m *defaultVhTextEmbeddingCsModel) InsertTx(ctx context.Context, tx *sql.Tx, data *VhTextEmbeddingCs) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?)", m.table, vhTextEmbeddingCsRowsExpectAutoSet)
	ret, err := tx.ExecContext(ctx, query, data.Id, data.Title, data.Content, data.Embeddings)
	return ret, err
}

func (m *defaultVhTextEmbeddingCsModel) UpdateTx(ctx context.Context, tx *sql.Tx, data *VhTextEmbeddingCs) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, vhTextEmbeddingCsRowsWithPlaceHolder)
	_, err := tx.ExecContext(ctx, query, data.Title, data.Content, data.Embeddings, data.Id)
	return err
}

func (m *defaultVhTextEmbeddingCsModel) DeleteTx(ctx context.Context, tx *sql.Tx, id string) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := tx.ExecContext(ctx, query, id)
	return err
}
