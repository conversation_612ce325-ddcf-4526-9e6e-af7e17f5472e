package common

import (
	"bytes"
	"github.com/PuerkitoBio/goquery"
	"strings"
	"time"
)

func InSlice(str string, s []string) bool {
	for _, v := range s {
		if str == v {
			return true
		}
	}
	return false
}

func IntSlice(str int, s []int) bool {
	for _, v := range s {
		if str == v {
			return true
		}
	}
	return false
}

func TimeToString(t time.Time) string {
	return t.Format("2006-01-02 15:04:05")
}

func HTMLToMarkdown(htmlStr string) string {
	// 创建 goquery 文档
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(htmlStr))
	if err != nil {
		return ""
	}

	// 用于收集 Markdown 内容的缓冲区
	var markdown bytes.Buffer

	// 遍历 HTML 节点
	doc.Find("body").Children().Each(func(i int, s *goquery.Selection) {
		switch s.Get(0).Data {
		case "h1", "h2", "h3", "h4", "h5", "h6":
			// 标题处理：只提取文本并添加换行
			text := strings.TrimSpace(s.Text())
			if text != "" {
				markdown.WriteString(text)
				markdown.WriteString("\n\n")
			}
		case "p", "div":
			// 段落处理：提取文本并添加换行
			text := strings.TrimSpace(s.Text())
			if text != "" {
				markdown.WriteString(text)
				markdown.WriteString("\n\n")
			}
		case "br":
			// 换行处理
			markdown.WriteString("\n")
		default:
			// 其他标签只提取文本（忽略 <img> 等）
			text := strings.TrimSpace(s.Text())
			if text != "" {
				markdown.WriteString(text)
				markdown.WriteString(" ")
			}
		}
	})

	// 清理多余的换行和空格
	result := strings.TrimSpace(markdown.String())
	result = strings.ReplaceAll(result, "\n\n\n", "\n\n")
	result = strings.ReplaceAll(result, "图源网络", "")
	result = strings.ReplaceAll(result, "仅供参考", "")

	return result
}
