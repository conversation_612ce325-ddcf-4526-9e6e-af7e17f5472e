package mdToHtml

import (
	"bytes"
	"fmt"
	"html/template"
	"regexp"
	"strings"

	"github.com/yuin/goldmark"
	goldmarkAst "github.com/yuin/goldmark/ast"
	"github.com/yuin/goldmark/parser"
	"github.com/yuin/goldmark/renderer"
	goldmarkHtml "github.com/yuin/goldmark/renderer/html"
	"github.com/yuin/goldmark/text"
	"github.com/yuin/goldmark/util"
)

// Converter 是将 Markdown 转换为 HTML 的转换器，支持自定义投票和商品卡片扩展
type Converter struct {
	markdown goldmark.Markdown
}

// New 创建一个新的 Converter 实例
func New() *Converter {
	// 配置 goldmark 解析器和渲染器
	md := goldmark.New(
		goldmark.WithExtensions(
			&voteExtension{},        // 添加投票扩展
			&productCardExtension{}, // 添加商品卡片扩展
		),
		goldmark.WithParserOptions(
			parser.WithAutoHeadingID(), // 自动生成标题 ID
		),
		goldmark.WithRendererOptions(
			goldmarkHtml.WithHardWraps(), // 使用硬换行
			goldmarkHtml.WithXHTML(),     // 使用 XHTML 格式
		),
	)

	return &Converter{
		markdown: md,
	}
}

// Convert 将 Markdown 转换为 HTML 字符串
func (c *Converter) Convert(md []byte) (string, error) {
	var buf bytes.Buffer
	// 使用 goldmark 转换 Markdown 到 HTML
	if err := c.markdown.Convert(md, &buf); err != nil {
		return "", fmt.Errorf("Markdown转换失败: %w", err)
	}

	// 包装成完整的 HTML 文档结构
	fullHTML := fmt.Sprintf(`<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>转换后的 Markdown</title>
	<link rel="stylesheet" href="./style.css">
</head>
<body>
	<div class="article-container">
		%s
	</div>
</body>
</html>`, buf.String())

	return fullHTML, nil
}

/**************************** 投票扩展 ****************************/

// voteExtension 实现了自定义投票扩展
type voteExtension struct{}

// Extend 将投票扩展添加到 goldmark 中
func (e *voteExtension) Extend(m goldmark.Markdown) {
	m.Parser().AddOptions(
		parser.WithBlockParsers(
			util.Prioritized(&voteParser{}, 200), // 添加投票块解析器
		),
	)
	m.Renderer().AddOptions(
		renderer.WithNodeRenderers(
			util.Prioritized(&voteHTMLRenderer{}, 200), // 添加投票块渲染器
		),
	)
}

// voteParser 解析投票块
type voteParser struct{}

// voteRegex 用于匹配投票块的开始标记
var voteRegex = regexp.MustCompile(`^:::vote\[([^\]]+)\](?:\{([^}]+)\})?\n`)

// Trigger 定义触发解析的字符
func (p *voteParser) Trigger() []byte {
	return []byte{':'}
}

// Open 解析投票块的开始部分
func (p *voteParser) Open(parent goldmarkAst.Node, reader text.Reader, pc parser.Context) (goldmarkAst.Node, parser.State) {
	line, _ := reader.PeekLine()
	// 检查是否以 ::: 开头
	if !bytes.HasPrefix(line, []byte(":::")) {
		return nil, parser.NoChildren
	}

	// 使用正则匹配投票块
	match := voteRegex.FindSubmatch(line)
	if match == nil {
		return nil, parser.NoChildren
	}

	// 前进读取位置
	reader.Advance(len(match[0]))

	// 提取投票 ID 和属性
	id := string(match[1])
	attrs := parseAttributes(string(match[2]))

	// 创建投票节点
	node := &voteNode{
		id:    id,
		attrs: attrs,
	}

	return node, parser.NoChildren
}

// Continue 解析投票块的内容
func (p *voteParser) Continue(node goldmarkAst.Node, reader text.Reader, pc parser.Context) parser.State {
	line, _ := reader.PeekLine()
	// 检查是否到达投票块结束标记
	if bytes.HasPrefix(line, []byte(":::")) {
		reader.AdvanceLine()
		return parser.Close
	}

	// 解析内容作为常规 Markdown
	child := pc.BlockParser().Parse(parentBlock(node), reader, pc)
	if child != nil {
		node.AppendChild(node, child)
	}

	return parser.Continue | parser.NoChildren
}

// Close 投票块解析结束时的处理
func (p *voteParser) Close(node goldmarkAst.Node, reader text.Reader, pc parser.Context) {
	// 无需特殊处理
}

// CanInterruptParagraph 投票块是否可以中断段落
func (p *voteParser) CanInterruptParagraph() bool {
	return true
}

// CanAcceptIndentedLine 投票块是否接受缩进行
func (p *voteParser) CanAcceptIndentedLine() bool {
	return false
}

// voteNode 表示一个投票块节点
type voteNode struct {
	goldmarkAst.BaseBlock
	id    string            // 投票 ID
	attrs map[string]string // 投票属性
}

// Dump 用于调试输出节点信息
func (n *voteNode) Dump(source []byte, level int) {
	goldmarkAst.DumpHelper(n, source, level, nil, nil)
}

// Kind 返回节点类型
func (n *voteNode) Kind() goldmarkAst.NodeKind {
	return goldmarkAst.KindFencedCodeBlock
}

// voteHTMLRenderer 将投票块渲染为 HTML
type voteHTMLRenderer struct {
	goldmarkHtml.Config
}

// RegisterFuncs 注册渲染函数
func (r *voteHTMLRenderer) RegisterFuncs(reg renderer.NodeRendererFuncRegisterer) {
	reg.Register(goldmarkAst.KindFencedCodeBlock, r.renderVote)
}

// renderVote 渲染投票块的具体实现
func (r *voteHTMLRenderer) renderVote(w util.BufWriter, source []byte, node goldmarkAst.Node, entering bool) (goldmarkAst.WalkStatus, error) {
	if !entering {
		return goldmarkAst.WalkContinue, nil
	}

	voteNode, ok := node.(*voteNode)
	if !ok {
		return goldmarkAst.WalkContinue, nil
	}

	// 提取标题和选项
	var title string
	var options []voteOption

	// 遍历子节点提取信息
	for child := node.FirstChild(); child != nil; child = child.NextSibling() {
		switch n := child.(type) {
		case *goldmarkAst.Heading:
			// 提取一级标题作为投票标题
			if n.Level == 1 {
				title = string(n.Text(source))
			}
		case *goldmarkAst.List:
			// 提取列表项作为投票选项
			for item := n.FirstChild(); item != nil; item = item.NextSibling() {
				if listItem, ok := item.(*goldmarkAst.ListItem); ok {
					// 检查是否是任务列表项
					taskCheckBox := listItem.FirstChild()
					if task, ok := taskCheckBox.(*goldmarkAst.TaskCheckBox); ok {
						// 获取选项文本
						textNode := task.NextSibling()
						if textNode != nil {
							text := strings.TrimSpace(string(textNode.Text(source)))
							value := extractValue(text)
							label := strings.TrimSpace(strings.Split(text, "{")[0])
							options = append(options, voteOption{
								Label: label,
								Value: value,
							})
						}
					}
				}
			}
		}
	}

	// 如果没有提取到标题，使用默认标题
	if title == "" {
		title = "请选择以下选项"
	}

	// 确定投票类型（单选/多选）
	voteType := "radio"
	maxSelect := ""
	if val, ok := voteNode.attrs[".multiple"]; ok && val == "" {
		voteType = "checkbox"
		if max, ok := voteNode.attrs[".max"]; ok {
			maxSelect = fmt.Sprintf(` data-max="%s"`, max)
		}
	}

	// 渲染投票块的 HTML 模板
	tmpl := `
<div class="vote-container" data-vote-id="{{.ID}}">
	<h3>{{.Title}}</h3>
	<div class="vote-options">
		{{range .Options}}
		<label class="vote-option">
			<input type="{{$.Type}}" name="{{$.ID}}" value="{{.Value}}"{{$.MaxSelect}}>
			<span>{{.Label}}</span>
		</label>
		{{end}}
	</div>
	<div class="vote-form">
		<button type="button" onclick="submitVote('{{.ID}}')">投票</button>
	</div>
</div>
<script>
function submitVote(voteId) {
	const container = document.querySelector(` + "`.vote-container[data-vote-id='${voteId}']`" + `);
	const inputs = container.querySelectorAll('input[type="radio"], input[type="checkbox"]:checked');
	const selected = Array.from(inputs).filter(input => input.checked).map(input => input.value);
	
	if (selected.length === 0) {
		alert('请至少选择一个选项');
		return;
	}
	
	// 检查最大选择数量限制
	const maxSelect = container.querySelector('input[type="checkbox"]')?.dataset.max;
	if (maxSelect && selected.length > parseInt(maxSelect)) {
		alert(` + "`最多可以选择 ${maxSelect} 个选项`" + `);
		return;
	}
	
	// 这里通常会发送投票数据到后端
	console.log('Vote submitted:', voteId, selected);
	alert('投票成功！');
}
</script>
`

	// 准备模板数据
	data := struct {
		ID        string
		Title     string
		Options   []voteOption
		Type      string
		MaxSelect string
	}{
		ID:        voteNode.id,
		Title:     title,
		Options:   options,
		Type:      voteType,
		MaxSelect: maxSelect,
	}

	// 解析并执行模板
	t, err := template.New("vote").Parse(tmpl)
	if err != nil {
		return goldmarkAst.WalkStop, fmt.Errorf("解析投票模板失败: %w", err)
	}

	if err := t.Execute(w, data); err != nil {
		return goldmarkAst.WalkStop, fmt.Errorf("执行投票模板失败: %w", err)
	}

	return goldmarkAst.WalkContinue, nil
}

// voteOption 表示投票选项
type voteOption struct {
	Label string // 选项显示文本
	Value string // 选项值
}

/**************************** 商品卡片扩展 ****************************/

// productCardExtension 实现了自定义商品卡片扩展
type productCardExtension struct{}

// Extend 将商品卡片扩展添加到 goldmark 中
func (e *productCardExtension) Extend(m goldmark.Markdown) {
	m.Parser().AddOptions(
		parser.WithBlockParsers(
			util.Prioritized(&productCardParser{}, 200), // 添加商品卡片解析器
		),
	)
	m.Renderer().AddOptions(
		renderer.WithNodeRenderers(
			util.Prioritized(&productCardHTMLRenderer{}, 200), // 添加商品卡片渲染器
		),
	)
}

// productCardParser 解析商品卡片块
type productCardParser struct{}

// productCardRegex 用于匹配商品卡片的开始标记
var productCardRegex = regexp.MustCompile(`^:::product-card\n`)

// Trigger 定义触发解析的字符
func (p *productCardParser) Trigger() []byte {
	return []byte{':'}
}

// Open 解析商品卡片的开始部分
func (p *productCardParser) Open(parent goldmarkAst.Node, reader text.Reader, pc parser.Context) (goldmarkAst.Node, parser.State) {
	line, _ := reader.PeekLine()
	// 检查是否以 ::: 开头
	if !bytes.HasPrefix(line, []byte(":::")) {
		return nil, parser.NoChildren
	}

	// 使用正则匹配商品卡片块
	match := productCardRegex.FindSubmatch(line)
	if match == nil {
		return nil, parser.NoChildren
	}

	// 前进读取位置
	reader.Advance(len(match[0]))

	// 创建商品卡片节点
	node := &productCardNode{
		attrs: make(map[string]string),
	}

	return node, parser.NoChildren
}

// Continue 解析商品卡片的内容
func (p *productCardParser) Continue(node goldmarkAst.Node, reader text.Reader, pc parser.Context) parser.State {
	line, _ := reader.PeekLine()
	// 检查是否到达商品卡片结束标记
	if bytes.HasPrefix(line, []byte(":::")) {
		reader.AdvanceLine()
		return parser.Close
	}

	// 检查 YAML 格式的前置属性
	if bytes.HasPrefix(line, []byte("---")) {
		reader.AdvanceLine()
		for {
			line, _ = reader.PeekLine()
			if bytes.HasPrefix(line, []byte("---")) {
				reader.AdvanceLine()
				break
			}
			parts := bytes.SplitN(line, []byte(":"), 2)
			if len(parts) == 2 {
				key := strings.TrimSpace(string(parts[0]))
				value := strings.TrimSpace(string(parts[1]))
				if n, ok := node.(*productCardNode); ok {
					n.attrs[key] = value
				}
			}
			reader.AdvanceLine()
		}
		return parser.Continue | parser.NoChildren
	}

	// 解析内容作为常规 Markdown
	child := pc.BlockParser().Parse(parentBlock(node), reader, pc)
	if child != nil {
		node.AppendChild(node, child)
	}

	return parser.Continue | parser.NoChildren
}

// Close 商品卡片解析结束时的处理
func (p *productCardParser) Close(node goldmarkAst.Node, reader text.Reader, pc parser.Context) {
	// 无需特殊处理
}

// CanInterruptParagraph 商品卡片是否可以中断段落
func (p *productCardParser) CanInterruptParagraph() bool {
	return true
}

// CanAcceptIndentedLine 商品卡片是否接受缩进行
func (p *productCardParser) CanAcceptIndentedLine() bool {
	return false
}

// productCardNode 表示一个商品卡片节点
type productCardNode struct {
	goldmarkAst.BaseBlock
	attrs map[string]string // 商品属性
}

// Dump 用于调试输出节点信息
func (n *productCardNode) Dump(source []byte, level int) {
	goldmarkAst.DumpHelper(n, source, level, nil, nil)
}

// Kind 返回节点类型
func (n *productCardNode) Kind() goldmarkAst.NodeKind {
	return goldmarkAst.KindFencedCodeBlock
}

// productCardHTMLRenderer 将商品卡片渲染为 HTML
type productCardHTMLRenderer struct {
	goldmarkHtml.Config
}

// RegisterFuncs 注册渲染函数
func (r *productCardHTMLRenderer) RegisterFuncs(reg renderer.NodeRendererFuncRegisterer) {
	reg.Register(goldmarkAst.KindFencedCodeBlock, r.renderProductCard)
}

// renderProductCard 渲染商品卡片的具体实现
func (r *productCardHTMLRenderer) renderProductCard(w util.BufWriter, source []byte, node goldmarkAst.Node, entering bool) (goldmarkAst.WalkStatus, error) {
	if !entering {
		return goldmarkAst.WalkContinue, nil
	}

	productNode, ok := node.(*productCardNode)
	if !ok {
		return goldmarkAst.WalkContinue, nil
	}

	// 提取商品信息
	var imageURL, altText, chineseTitle, englishTitle, price, sold string

	// 遍历子节点提取信息
	for child := node.FirstChild(); child != nil; child = child.NextSibling() {
		switch n := child.(type) {
		case *goldmarkAst.Image:
			// 提取商品图片
			imageURL = string(n.Destination)
			altText = string(n.Text(source))
		case *goldmarkAst.Paragraph:
			text := string(n.Text(source))
			// 提取中文标题
			if strings.HasPrefix(text, "!! ") {
				chineseTitle = strings.TrimPrefix(text, "!! ")
			} else if strings.HasPrefix(text, "!!! ") {
				// 提取英文标题
				englishTitle = strings.TrimPrefix(text, "!!! ")
			} else if strings.Contains(text, "**价格：**") {
				// 提取价格
				price = strings.TrimSpace(strings.Split(text, "**价格：**")[1])
			} else if strings.Contains(text, "**已售：**") {
				// 提取销量
				sold = strings.TrimSpace(strings.Split(text, "**已售：**")[1])
			}
		}
	}

	// 如果没有设置 ID，使用默认值
	id := productNode.attrs["id"]
	if id == "" {
		id = "product-default"
	}

	// 如果没有图片URL，使用默认图片
	if imageURL == "" {
		imageURL = "https://via.placeholder.com/150"
	}

	// 渲染商品卡片的 HTML 模板
	tmpl := `
<div class="product-card" data-product-id="{{.ID}}">
	<div class="product-card-image">
		<img src="{{.ImageURL}}" alt="{{.AltText}}">
	</div>
	<div class="product-card-content">
		<div class="product-card-main">
			<h3 class="product-card-chinese-title">{{.ChineseTitle}}</h3>
			<h4 class="product-card-english-title">{{.EnglishTitle}}</h4>
			<div class="product-card-info">
				<span class="product-card-price"><strong>价格：</strong>{{.Price}}</span>
				<span class="product-card-sold"><strong>已售：</strong>{{.Sold}}</span>
			</div>
		</div>
		<div class="product-card-action">
			<button class="product-card-buy-btn" onclick="buyProduct('{{.ID}}')">→</button>
		</div>
	</div>
</div>
<script>
function buyProduct(productId) {
	// 这里通常会处理购买操作
	console.log('Buy product:', productId);
	alert(` + "`已添加商品 ${productId} 到购物车`" + `);
}
</script>
`

	// 准备模板数据
	data := struct {
		ID           string
		ImageURL     string
		AltText      string
		ChineseTitle string
		EnglishTitle string
		Price        string
		Sold         string
	}{
		ID:           id,
		ImageURL:     imageURL,
		AltText:      altText,
		ChineseTitle: chineseTitle,
		EnglishTitle: englishTitle,
		Price:        price,
		Sold:         sold,
	}

	// 解析并执行模板
	t, err := template.New("productCard").Parse(tmpl)
	if err != nil {
		return goldmarkAst.WalkStop, fmt.Errorf("解析商品卡片模板失败: %w", err)
	}

	if err := t.Execute(w, data); err != nil {
		return goldmarkAst.WalkStop, fmt.Errorf("执行商品卡片模板失败: %w", err)
	}

	return goldmarkAst.WalkContinue, nil
}

/**************************** 辅助函数 ****************************/

// parentBlock 获取父级块节点
func parentBlock(node goldmarkAst.Node) goldmarkAst.Node {
	for n := node; n != nil; n = n.Parent() {
		if _, ok := n.(goldmarkAst.Node); ok {
			return n
		}
	}
	return nil
}

// parseAttributes 解析属性字符串为键值对
func parseAttributes(attrStr string) map[string]string {
	attrs := make(map[string]string)
	if attrStr == "" {
		return attrs
	}

	// 分割属性字符串
	parts := strings.Fields(attrStr)
	for _, part := range parts {
		if part == "" {
			continue
		}
		if strings.Contains(part, "=") {
			// 处理键值对属性
			kv := strings.SplitN(part, "=", 2)
			if len(kv) == 2 {
				attrs[kv[0]] = strings.Trim(kv[1], `"`)
			}
		} else {
			// 处理布尔属性
			attrs[part] = ""
		}
	}

	return attrs
}

// extractValue 从文本中提取选项值
func extractValue(text string) string {
	start := strings.Index(text, "{value=")
	if start == -1 {
		return ""
	}
	start += len("{value=")
	end := strings.Index(text[start:], "}")
	if end == -1 {
		return ""
	}
	return text[start : start+end]
}
