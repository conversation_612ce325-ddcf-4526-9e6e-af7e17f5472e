/* From 积分兑换策略比较分析 - DeepSeek_files/css2 */
/* cyrillic-ext */
@font-face {
    font-family: 'Roboto Mono';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/robotomono/v30/L0x5DF4xlVMF-BfR8bXMIjhGq3-cXbKDO1w.woff2) format('woff2');
    unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
    font-family: 'Roboto Mono';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/robotomono/v30/L0x5DF4xlVMF-BfR8bXMIjhPq3-cXbKDO1w.woff2) format('woff2');
    unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek */
@font-face {
    font-family: 'Roboto Mono';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/robotomono/v30/L0x5DF4xlVMF-BfR8bXMIjhIq3-cXbKDO1w.woff2) format('woff2');
    unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
    font-family: 'Roboto Mono';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/robotomono/v30/L0x5DF4xlVMF-BfR8bXMIjhEq3-cXbKDO1w.woff2) format('woff2');
    unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
    font-family: 'Roboto Mono';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/robotomono/v30/L0x5DF4xlVMF-BfR8bXMIjhFq3-cXbKDO1w.woff2) format('woff2');
    unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
    font-family: 'Roboto Mono';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/robotomono/v30/L0x5DF4xlVMF-BfR8bXMIjhLq3-cXbKD.woff2) format('woff2');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
    font-family: 'Roboto Mono';
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/robotomono/v30/L0x5DF4xlVMF-BfR8bXMIjhGq3-cXbKDO1w.woff2) format('woff2');
    unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
    font-family: 'Roboto Mono';
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/robotomono/v30/L0x5DF4xlVMF-BfR8bXMIjhPq3-cXbKDO1w.woff2) format('woff2');
    unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek */
@font-face {
    font-family: 'Roboto Mono';
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/robotomono/v30/L0x5DF4xlVMF-BfR8bXMIjhIq3-cXbKDO1w.woff2) format('woff2');
    unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
    font-family: 'Roboto Mono';
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/robotomono/v30/L0x5DF4xlVMF-BfR8bXMIjhEq3-cXbKDO1w.woff2) format('woff2');
    unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
    font-family: 'Roboto Mono';
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/robotomono/v30/L0x5DF4xlVMF-BfR8bXMIjhFq3-cXbKDO1w.woff2) format('woff2');
    unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
    font-family: 'Roboto Mono';
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/robotomono/v30/L0x5DF4xlVMF-BfR8bXMIjhLq3-cXbKD.woff2) format('woff2');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* Custom styles for the article */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 0px 20px 20px;
    background-color: #FFFFFF;
    color: #000000;
}

.article-container {
    max-width: 800px;
    margin: 0 auto;
    background-color: #fff;
    padding: 0px;
    border-radius: 8px;

}

h1 {
    font-size: 1.3em;
    color: #000000;
    margin-bottom: 5px;
}

/* Center only page title on video.html */
.article-container > h1.page-title {
    text-align: center;
}

.article-subtitle {
    font-size: 1.1em;
    color: #666666;
    text-align: left;
    margin-bottom: 0px;
    font-style: italic;
    font-weight: 300;
    display: flex;
    align-items: flex-end;
    min-height: 1.0em;
}

.article-date {
    font-size: 0.9em;
    color: #666666;
    text-align: left;
    margin-bottom: 0px;
    font-style: italic;
    display: flex;
    align-items: flex-start;
    min-height: 1.0em;
}

h2 {
    font-size: 1.2em;
    color: #000000;
    margin-top: 40px;
    margin-bottom: 20px;
    padding-left: 0px;
}

ul {
    list-style-type: none;
    padding-left: 2px;
}

ol {
    padding-left: 2px;
}

li {
    margin-bottom: 8px;
    font-size: 1.1rem;
    position: relative;
    padding-left: 25px;
    list-style: none;
}

li::before {
    content: '🍇';
    position: absolute;
    left: 0;
    top: 0.2em;
    font-size: 1rem;
    color: #800020; /* Burgundy */
}

strong {
    color: #000000;
}

li ul {
    margin-top: 10px;
    padding-left: 20px;
    border-left: 1px dashed #ccc;
}

ol li ul {
    margin-top: 10px;
    padding-left: 20px;
    border-left: 1px dashed #ccc;
}

li ul li {
    font-size: 1.1rem;
    margin-bottom: 6px;
    list-style-type: '– ';
    padding-left: 5px;
}

li ul li::before {
    content: none;
}

p {
    margin-bottom: 20px;
    font-size: 1.1rem;
}

figure {
    margin: 20px auto;
    text-align: center;
}

figure img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
}

/* Ensure videos are fully responsive without causing horizontal scroll */
figure video {
    display: block;
    width: 100%;
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-sizing: border-box;
}

figcaption {
    margin-top: 3px;
    font-size: 0.9em;
    color: #555;
}


/* Style for the final paragraph */
.conclusion {
    margin-top: 40px;
    padding-top: 20px;
    border-top: 2px solid #000000;
    font-style: italic;
    color: #000000;
}

/* Table styles */
table {
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%;
}

th {
    border-bottom: 2px solid #BBBBBB;
    padding: 5px 10px;
    white-space: nowrap;
}

td {
    border-bottom: 1px solid #E5E5E5;
    padding: 5px 10px;
    white-space: nowrap;
}

/* Blockquote styles */
blockquote {
    margin: 20px 0;
    padding: 15px 20px;
    background-color: #f9f9f9;
    font-style: italic;
    color: #555;
}

blockquote p {
    margin: 0;
    font-size: 1.1rem;
}

.vote-container {
    margin: 16px 0;
    padding: 16px;
    border: 1px solid #e8f1f8;
    border-radius: 8px;
    background-color: #f8fbfe;
    box-shadow: 0 1px 3px rgba(0, 124, 186, 0.06);
    transition: all 0.2s ease;
}

.vote-container:hover {
    box-shadow: 0 2px 8px rgba(0, 124, 186, 0.12);
    border-color: #d0e3f0;
}

.vote-container h3 {
    margin-top: 0;
    margin-bottom: 12px;
    font-size: 1em;
    color: #2c3e50;
    font-weight: 600;
    text-align: center;
}

.vote-options {
    margin: 10px 0 12px 0;
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.vote-option {
    display: flex;
    align-items: center;
    margin: 0;
    padding: 8px 12px;
    background-color: #ffffff;
    border: 1px solid #e8f1f8;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.15s ease;
    position: relative;
    user-select: none;
}

.vote-option:hover {
    background-color: #f5f9ff;
    border-color: #b8d4f0;
    transform: translateY(-1px);
    box-shadow: 0 1px 4px rgba(0, 124, 186, 0.1);
}

.vote-option input[type="radio"],
.vote-option input[type="checkbox"] {
    margin: 0 10px 0 0;
    width: 16px;
    height: 16px;
    accent-color: #007cba;
    cursor: pointer;
}

.vote-option:has(input:checked) {
    background-color: #f0f7ff;
    border-color: #007cba;
    color: #006699;
    font-weight: 500;
}

.vote-form {
    text-align: center;
}

.vote-form button {
    margin: 8px 0 0 0;
    padding: 8px 16px;
    border: none;
    border-radius: 5px;
    background: linear-gradient(135deg, #007cba 0%, #005a87 100%);
    color: white;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 124, 186, 0.15);
    min-width: 90px;
    display: inline-block;
}

.vote-form button:hover {
    background: linear-gradient(135deg, #005a87 0%, #004666 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 124, 186, 0.25);
}

.vote-form button:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(0, 124, 186, 0.2);
}

.vote-form button:disabled {
    background: linear-gradient(135deg, #d0d0d0 0%, #b0b0b0 100%);
    color: #666666;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 1px 2px rgba(176, 176, 176, 0.15);
}

.vote-form button:disabled:hover {
    background: linear-gradient(135deg, #d0d0d0 0%, #b0b0b0 100%);
    transform: none;
    box-shadow: 0 1px 2px rgba(176, 176, 176, 0.15);
}

.vote-result-item {
    position: relative;
    background-color: #ffffff;
    border: 1px solid #e8f1f8;
    border-radius: 5px;
    margin: 0;
    padding: 8px 12px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: auto;
    transition: all 0.15s ease;
    font-size: 0.9rem;
    user-select: none;
    cursor: default;
}

.vote-result-item:hover {
    background-color: #f5f9ff;
    border-color: #b8d4f0;
    transform: translateY(-1px);
    box-shadow: 0 1px 4px rgba(0, 124, 186, 0.1);
}

.vote-result-text {
    position: relative;
    z-index: 2;
    flex: 1;
    display: flex;
    align-items: center;
}

.vote-result-count {
    position: relative;
    z-index: 2;
    font-size: 0.9em;
    color: #333;
    display: flex;
    align-items: center;
    margin-left: 10px;
}

.vote-progress {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    background-color: #007cba;
    border-radius: 4px;
    opacity: 0.2;
    z-index: 1;
    transition: width 0.5s ease-in-out;
}

/* Product Card Styles */
.product-card {
    margin: 12px 0;
    border: 2px solid #ffe6e6;
    border-radius: 12px;
    background: linear-gradient(135deg, #ffffff 0%, #fff5f5 100%);
    overflow: hidden;
    box-shadow: 0 3px 12px rgba(202, 15, 27, 0.1);
    transition: all 0.3s ease;
    display: flex !important;
    flex-direction: row !important;
    align-items: stretch;
    aspect-ratio: 16 / 7;
    max-width: 100%;
    min-height: 100px;
    width: 100%;
}

.product-card:hover {
    box-shadow: 0 6px 20px rgba(202, 15, 27, 0.15);
    transform: translateY(-3px);
    border-color: #CA0F1B;
}

.product-card-image {
    flex: 0 0 18% !important;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #fff0f0 0%, #ffe6e6 100%);
    padding: 6px;
    order: 1 !important;
    box-sizing: border-box;
}

.product-card-image img {
    width: 100%;
    height: auto;
    max-height: 100%;
    object-fit: contain;
    border-radius: 8px;
    display: block;
}

.product-card-content {
    flex: 1 !important;
    padding: 8px 12px;
    display: flex;
    justify-content: space-between;
    align-items: stretch;
    order: 2 !important;
    margin-top: 3px;
    min-width: 0;
    box-sizing: border-box;
    gap: 3px;
}

.product-card-title {
    margin: 0 0 8px 0;
    font-size: 1em;
    color: #CA0F1B;
    font-weight: normal;
    line-height: 1.2;
}

.product-card-info {
    display: flex;
    gap: 8px;
    margin-bottom: 0px;
    flex-wrap: wrap;
}

.product-card-price {
    font-size: 0.85rem;
    color: #CA0F1B;
    font-weight: bold;
}

.product-card-sold {
    font-size: 0.85rem;
    color: #666666;
    font-weight: normal;
}

.product-card-price strong {
    color: #CA0F1B;
}

.product-card-sold strong {
    color: #666666;
}

.product-card-description {
    margin-top: auto;
    font-size: 0.8rem;
    color: #CA0F1B;
    line-height: 1.2;
    font-style: italic;
}

.product-card-main {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.product-card-title {
    margin: 0 0 4px 0;
    font-size: 0.85rem;
    color: #CA0F1B;
    font-weight: normal;
    line-height: 1.1;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.product-card-chinese-title {
    margin: 0 0 2px 0;
    font-size: 0.85rem;
    color: #000000;
    font-weight: normal;
    line-height: 1.1;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.product-card-english-title {
    margin: 0 0 4px 0;
    font-size: 0.75rem;
    color: #666;
    font-weight: normal;
    line-height: 1.1;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.product-card-action {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0px;
    flex-shrink: 0;
    min-width: 28px;
}

.product-card-buy-btn {
    width: 28px;
    height: 28px;
    border: none;
    border-radius: 6px;
    background: transparent;
    color: #CA0F1B;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    box-shadow: none;
}

.product-card-buy-btn:hover {
    background-color: rgba(202, 15, 27, 0.1);
    transform: scale(1.1);
    box-shadow: none;
}

.arrow-icon {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: #666666;
    transform: translateX(1px);
}

/* Responsive design for product card */
@media (min-width: 769px) {
    .product-card {
        flex-direction: row !important;
        align-items: stretch;
    }

    .product-card-image {
        flex: 0 0 30% !important;
        order: 1 !important;
    }

    .product-card-content {
        flex: 1 !important;
        order: 2 !important;
    }
}

@media (max-width: 768px) {
    .product-card {
        flex-direction: column;
        aspect-ratio: auto;
        min-height: auto;
        margin: 10px 0;
    }

    .product-card-image {
        flex: 0 0 auto;
        aspect-ratio: 16 / 9;
        order: 1;
        padding: 6px;
    }

    .product-card-content {
        order: 2;
        padding: 8px;
    }

    .product-card-info {
        gap: 8px;
    }
}
