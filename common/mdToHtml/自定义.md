# Markdown投票标记扩展规范

## 语法定义

### 投票模块基本语法
```markdown
:::vote[投票ID]{.single}
# 投票标题

- [ ] 选项1{value=option1}
- [ ] 选项2{value=option2}  
- [ ] 选项3{value=option3}
:::
```

### 语法说明
- `:::vote[投票ID]` - 开始标记，投票ID用于唯一标识该投票，右边的{}为扩展属性，用于标识投票选项属性
- 投票内容区域支持标准Markdown格式
- 使用`- [ ]`表示投票选项（单选）
- `{value=选项值}`表示选项的提交值，用于数据处理和统计
- `:::` - 结束标记

### 扩展属性
```markdown
:::vote[投票ID]{.single}
:::vote[投票ID]{.multiple .max=3}
```

#### 属性说明
.single - 单选模式（默认）
.multiple - 多选模式
.max=数字 - 多选模式下的最大选择数量

### 商品卡片模块基本语法
```markdown
:::product-card
---
id: 12345
---
![商品缩略图](图片URL "alt文字")
!! 商品中文标题
!!! 商品英文标题
**价格：** ¥199.99
**已售：** 1,234件
:::
```

### 语法说明

:::product-card 和 ::: 用作容器标记，标识这是一个商品卡片
![商品缩略图](图片URL "alt文字") 商品缩略图，支持标准Markdown图片语法
!! 表示商品中标题
!!! 表示商品英文标题
```
属性区块：紧随其后，用 --- 开始和结束
每行一个键值对，格式：key: value
建议遵循 YAML 子集（仅支持简单的字符串/数字），这样解析器容易实现。
id 必填，其余可选。
```
**价格：** ¥199.99 使用粗体标记价格信息
**已售：** 1,234件 使用粗体标记销量信息