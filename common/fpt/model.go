package fpt

const (
	PfpDm = "007"
	ZfpDm = "004"
)

type InvoiceInfo struct {
	Spbm      string
	GoodsName string
	PayNumber int64
	Unit      string
	PayMoney  float64
	Rate      float64
	SumRate   float64
	Tax       float64
	SumTax    float64
}

type CreateResult struct {
	Fpqqlsh string `xml:"FPQQLSH"`
	FpQm    string `xml:"FP_DM"`
	FpHm    string `xml:"FP_HM"`
	Jym     string `xml:"JYM"`
	Kprq    string `xml:"KPRQ"`
	PdfUrl  string `xml:"PDF_URL"`
	SpUrl   string `xml:"SP_URL"`
}

type AbandonmentResult struct {
	PdfUrl string `xml:"PDF_URL"`
}

type QueryResult struct {
	Fpqqlsh string `xml:"FPQQLSH"`
	FpQm    string `xml:"FP_DM"`
	FpHm    string `xml:"FP_HM"`
	Jym     string `xml:"JYM"`
	Kprq    string `xml:"KPRQ"`
	PdfUrl  string `xml:"PDF_URL"`
	SpUrl   string `xml:"SP_URL"`
	Fpzt    string `xml:"FPZT"`
}
