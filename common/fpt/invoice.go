package fpt

import (
	"bytes"
	"crypto/aes"
	"crypto/md5"
	"crypto/tls"
	"encoding/base64"
	"encoding/xml"
	"engine/common/soap"
	"fmt"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/utils"
	"github.com/beevik/etree"
	"github.com/zeromicro/go-zero/core/logx"
	"net/http"
	"time"
)

type Client struct {
	Config InvoiceConfig
}

func New(config InvoiceConfig) *Client {
	return &Client{Config: config}
}

// GetCreateContent 获取创建发票需要的内容 content
func GetCreateContent(orderSn string, xsfNsrsbh string, xsfMc string, xsfDzsh string, xsfYhzh string, taxpayer string, msgMc string, telephone string, email string, valorem float64, hjje float64, tax float64, orderDetail []*InvoiceInfo) string {
	var detailStr string

	//产品信息
	for _, info := range orderDetail {
		detailStr += fmt.Sprintf("<COMMON_FPKJ_XMXX>"+
			"<FPHXZ>0</FPHXZ>"+
			"<SPBM>%s</SPBM>"+
			"<ZXBM></ZXBM>"+
			"<YHZCBS></YHZCBS>"+
			"<LSLBS></LSLBS>"+
			"<ZZSTSGL></ZZSTSGL>"+
			"<XMMC>%s</XMMC>"+
			"<GGXH></GGXH>"+
			"<DW>%s</DW>"+
			"<XMSL>%d</XMSL>"+
			"<XMDJ>%.6f</XMDJ>"+
			"<XMJE>%.2f</XMJE>"+
			"<SL>%.2f</SL>"+
			"<SE>%.2f</SE>"+
			"<BY1></BY1>"+
			"<BY2></BY2>"+
			"<BY3></BY3>"+
			"<BY4></BY4>"+
			"<BY5></BY5>"+
			"</COMMON_FPKJ_XMXX>",
			info.Spbm, info.GoodsName, info.Unit, info.PayNumber, info.Rate, info.SumRate, info.Tax, info.SumTax)
	}

	//发票信息
	return fmt.Sprintf("<REQUEST_COMMON_FPKJ class=\"REQUEST_COMMON_FPKJ\">"+
		"<FPQQLSH>%s</FPQQLSH>"+
		"<KPLX>0</KPLX>"+
		"<ZSFS>0</ZSFS>"+
		"<XSF_NSRSBH>%s</XSF_NSRSBH>"+
		"<XSF_MC>%s</XSF_MC>"+
		"<XSF_DZDH>%s</XSF_DZDH>"+
		"<XSF_YHZH>%s</XSF_YHZH>"+
		"<GMF_NSRSBH>%s</GMF_NSRSBH>"+
		"<GMF_MC>%s</GMF_MC>"+
		"<GMF_DZDH></GMF_DZDH>"+
		"<GMF_YHZH></GMF_YHZH>"+
		"<GMF_SJH>%s</GMF_SJH>"+
		"<GMF_DZYX>%s</GMF_DZYX>"+
		"<FPT_ZH></FPT_ZH>"+
		"<WX_OPENID></WX_OPENID>"+
		"<KPR>胡管玥</KPR>"+
		"<SKR>胡管玥</SKR>"+
		"<FHR>王娟</FHR>"+
		"<YFP_DM></YFP_DM>"+
		"<YFP_HM></YFP_HM>"+
		"<JSHJ>%.2f</JSHJ>"+
		"<HJJE>%.2f</HJJE>"+
		"<HJSE>%.2f</HJSE>"+
		"<KCE>0</KCE>"+
		"<BZ></BZ>"+
		"<HYLX>0</HYLX>"+
		"<BY1></BY1>"+
		"<BY2></BY2>"+
		"<BY3></BY3>"+
		"<BY4></BY4>"+
		"<BY5></BY5>"+
		"<BY6></BY6>"+
		"<BY7></BY7>"+
		"<BY8></BY8>"+
		"<BY9></BY9>"+
		"<BY10></BY10>"+
		"<COMMON_FPKJ_XMXXS class=\"COMMON_FPKJ_XMXX\" size=\"1\">%s</COMMON_FPKJ_XMXXS>"+
		"</REQUEST_COMMON_FPKJ>",
		orderSn, xsfNsrsbh, xsfMc, xsfDzsh, xsfYhzh, taxpayer, msgMc, telephone, email, hjje, valorem, tax, detailStr)
}

func GetCreateRedContent(orderSn string, yfpDm string, yfpHm string, xsfNsrsbh string, xsfMc string, xsfDzsh string, xsfYhzh string, taxpayer string, msgMc string, telephone string, email string, valorem float64, hjje float64, tax float64, orderDetail []*InvoiceInfo) string {
	var detailStr string

	//产品信息
	for _, info := range orderDetail {
		detailStr += fmt.Sprintf("<COMMON_FPKJ_XMXX>"+
			"<KPLX>1</KPLX>"+
			"<YFP_DM>%s</YFP_DM>"+ //原发票代码
			"<YFP_HM>%s</YFP_HM>"+ //原发票号码
			"<FPHXZ>0</FPHXZ>"+
			"<SPBM>%s</SPBM>"+
			"<ZXBM></ZXBM>"+
			"<YHZCBS></YHZCBS>"+
			"<LSLBS></LSLBS>"+
			"<ZZSTSGL></ZZSTSGL>"+
			"<XMMC>%s</XMMC>"+
			"<GGXH></GGXH>"+
			"<DW>%s</DW>"+
			"<XMSL>%d</XMSL>"+
			"<XMJE>-%.2f</XMJE>"+
			"<SL>%.2f</SL>"+
			"<SE>-%.2f</SE>"+
			"<BY1></BY1>"+
			"<BY2></BY2>"+
			"<BY3></BY3>"+
			"<BY4></BY4>"+
			"<BY5></BY5>"+
			"</COMMON_FPKJ_XMXX>",
			yfpDm, yfpHm, info.Spbm, info.GoodsName, info.Unit, info.PayNumber, info.PayMoney, info.Tax, info.Rate)
	}

	//发票信息
	return fmt.Sprintf("<REQUEST_COMMON_FPKJ class=\"REQUEST_COMMON_FPKJ\">"+
		"<FPQQLSH>%s</FPQQLSH>"+
		"<KPLX>0</KPLX>"+
		"<ZSFS>0</ZSFS>"+
		"<XSF_NSRSBH>%s</XSF_NSRSBH>"+
		"<XSF_MC>%s</XSF_MC>"+
		"<XSF_DZDH>%s</XSF_DZDH>"+
		"<XSF_YHZH>%s</XSF_YHZH>"+
		"<GMF_NSRSBH>%s</GMF_NSRSBH>"+
		"<GMF_MC>%s</GMF_MC>"+
		"<GMF_DZDH></GMF_DZDH>"+
		"<GMF_YHZH></GMF_YHZH>"+
		"<GMF_SJH>%s</GMF_SJH>"+
		"<GMF_DZYX>%s</GMF_DZYX>"+
		"<FPT_ZH></FPT_ZH>"+
		"<WX_OPENID></WX_OPENID>"+
		"<KPR>胡管玥</KPR>"+
		"<SKR>胡管玥</SKR>"+
		"<FHR>王娟</FHR>"+
		"<YFP_DM></YFP_DM>"+
		"<YFP_HM></YFP_HM>"+
		"<JSHJ>-%.2f</JSHJ>"+
		"<HJJE>-%.2f</HJJE>"+
		"<HJSE>-%.2f</HJSE>"+
		"<KCE>0</KCE>"+
		"<BZ></BZ>"+
		"<HYLX>0</HYLX>"+
		"<BY1></BY1>"+
		"<BY2></BY2>"+
		"<BY3></BY3>"+
		"<BY4></BY4>"+
		"<BY5></BY5>"+
		"<BY6></BY6>"+
		"<BY7></BY7>"+
		"<BY8></BY8>"+
		"<BY9></BY9>"+
		"<BY10></BY10>"+
		"<COMMON_FPKJ_XMXXS class=\"COMMON_FPKJ_XMXX\" size=\"1\">%s</COMMON_FPKJ_XMXXS>"+
		"</REQUEST_COMMON_FPKJ>",
		orderSn, xsfNsrsbh, xsfMc, xsfDzsh, xsfYhzh, taxpayer, msgMc, telephone, email, hjje, valorem, tax, detailStr)
}

func GetCreateXml(appId string, interfaceCode string, content string, contentKey string) string {
	exchangeId := interfaceCode + time.Now().Format("2006-01-02") + utils.RandStringBytes(9)
	return fmt.Sprintf("<?xml version='1.0' encoding='UTF-8' ?>"+
		"<interface xmlns=\"\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:schemaLocation=\"http://www.chinatax.gov.cn/tirip/dataspec/interfaces.xsd\" version=\""+
		"DZFPQZ0.2"+"\"> "+
		"<globalInfo>"+
		"<appId>%s</appId>"+
		"<interfaceId></interfaceId>"+
		"<interfaceCode>%s</interfaceCode>"+
		"<requestCode>DZFPQZ</requestCode>"+
		"<requestTime>%s</requestTime><responseCode>Ds</responseCode>"+
		"<dataExchangeId>DZFPQZ%s</dataExchangeId>"+
		"</globalInfo>"+
		"<returnStateInfo>"+
		"<returnCode></returnCode>"+
		"<returnMessage></returnMessage>"+
		"</returnStateInfo>"+
		"<Data>"+
		"<dataDescription>"+
		"<zipCode>0</zipCode>"+
		"</dataDescription>"+
		"<content>%s</content>"+
		"<contentKey>%s</contentKey>"+
		"</Data>"+
		"</interface>",
		appId, interfaceCode, time.Now().Format("2006-01-02 15:04:05"), exchangeId, content, contentKey)
}

func (c *Client) SoapRequest(content []byte, interfaceCode string) (*CommonResp, error) {
	//转base64
	conBase64 := base64.StdEncoding.EncodeToString(content)

	//转md5
	conMd5 := fmt.Sprintf("%x", md5.Sum([]byte(conBase64)))

	//aes加密
	e := EcbEncrypt([]byte(conMd5), []byte(c.Config.Key))

	//组装请求参数
	params := soap.Params{
		"in0": GetCreateXml(c.Config.AppId, interfaceCode, conBase64, base64.StdEncoding.EncodeToString(e)),
	}

	//添加证书
	clientCrt, err := tls.LoadX509KeyPair(c.Config.CertCrtPath, c.Config.CertPemPath)
	if err != nil {
		return nil, fmt.Errorf("无效的证书")
	}

	sp, err := soap.SoapClientWithConfig(CreateSoapUrl, &http.Client{
		Timeout: 3000 * time.Millisecond,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				Certificates: []tls.Certificate{clientCrt},
			},
		},
	}, &soap.Config{Dump: false})

	if err != nil {
		return nil, fmt.Errorf("初始化发票通失败")
	}

	resp, err := sp.Call("doService", params)
	if err != nil {
		return nil, fmt.Errorf( /*"请求发票通开票失败:%s", */ err.Error())
	}

	result := new(CommonResp)

	err = resp.Unmarshal(result)
	if err != nil {
		return nil, fmt.Errorf("发票通返回值验证失败:%s", err.Error())
	}
	return result, nil
}

// Create 开票 只支持单张 需要同时开票多张可用协程处理
func (c *Client) Create(orderSn string, valorem float64, hjje float64, tax float64, taxpayer string, name string, tel string, email string, od []*InvoiceInfo) (*CreateResult, error) {
	//组装 content xml数据
	content := GetCreateContent(orderSn, c.Config.XsfNsrsbh,
		c.Config.XsfMc, c.Config.XsfDzsh, c.Config.XsfYhzh, taxpayer, name, tel,
		email, valorem, hjje, tax, od)
	//return nil, errors.New(content)

	//发送请求
	result, err := c.SoapRequest([]byte(content), c.Config.CreateInterfaceCode)
	if err != nil {
		return nil, err
	}

	//解析返回数据
	doc := etree.NewDocument()
	err = doc.ReadFromString(result.Out)
	if err != nil {
		return nil, fmt.Errorf("发票通返回结构验证失败:%s", err.Error())
	}

	itfc := doc.SelectElement("interface")
	resultInfo := itfc.SelectElement("returnStateInfo")

	//验证是否成功
	if resultInfo.SelectElement("returnCode").Text() != "0000" {
		return nil, fmt.Errorf(resultInfo.SelectElement("returnMessage").Text())
	}

	//解析响应数据
	createResult := new(CreateResult)
	respContent := itfc.SelectElement("Data").SelectElement("content").Text()
	respContentBase64, err := base64.StdEncoding.DecodeString(respContent)
	if err != nil {
		logx.Error(fmt.Sprintf("订单:%s 开票成功 但base64 content失败 err: %s", orderSn, err.Error()))
	} else {
		err = xml.Unmarshal(respContentBase64, createResult)
		if err != nil {
			logx.Error(fmt.Sprintf("订单:%s 开票成功 但解析返回content失败 err: %s", orderSn, err.Error()))
		}
	}

	return createResult, nil
}

// CreateRed 开票冲红 只支持单张 需要同时开票多张可用协程处理
func (c *Client) CreateRed(orderSn string, yfpDm string, yfpHm string, valorem float64, hjje float64, tax float64, taxpayer string, name string, tel string, email string, od []*InvoiceInfo) (*CreateResult, error) {
	//组装 content xml数据
	content := GetCreateRedContent(orderSn, yfpDm, yfpHm, c.Config.XsfNsrsbh,
		c.Config.XsfMc, c.Config.XsfDzsh, c.Config.XsfYhzh, taxpayer, name, tel,
		email, valorem, hjje, tax, od)

	//发送请求
	result, err := c.SoapRequest([]byte(content), c.Config.CreateInterfaceCode)
	if err != nil {
		return nil, err
	}

	//解析返回数据
	doc := etree.NewDocument()
	err = doc.ReadFromString(result.Out)
	if err != nil {
		return nil, fmt.Errorf("发票通返回结构验证失败:%s", err.Error())
	}

	itfc := doc.SelectElement("interface")
	resultInfo := itfc.SelectElement("returnStateInfo")

	//验证是否成功
	if resultInfo.SelectElement("returnCode").Text() != "0000" {
		return nil, fmt.Errorf(resultInfo.SelectElement("returnMessage").Text())
	}

	//解析响应数据
	createResult := new(CreateResult)
	respContent := itfc.SelectElement("Data").SelectElement("content").Text()
	respContentBase64, err := base64.StdEncoding.DecodeString(respContent)
	if err != nil {
		logx.Error(fmt.Sprintf("订单:%s 开票成功 但base64 content失败 err: %s", orderSn, err.Error()))
	} else {
		err = xml.Unmarshal(respContentBase64, createResult)
		if err != nil {
			logx.Error(fmt.Sprintf("订单:%s 开票成功 但解析返回content失败 err: %s", orderSn, err.Error()))
		}
	}

	return createResult, nil
}

// GetPh 根据发票类型获取发票盘号
func (c *Client) GetPh(fplxdm string) string {
	if fplxdm == PfpDm {
		return c.Config.PPh
	} else if fplxdm == ZfpDm {
		return c.Config.ZPh
	}
	return ""
}

// GetAbandonmentContent 获取发票作废需要的内容
func GetAbandonmentContent(xsfNsrsbh string, skpbh string, fplxdm string, fpdm string, fphm string, hjje float64) string {
	//空票作废
	/*return fmt.Sprintf("<REQUEST_COMMON_KBZFCX class=\"REQUEST_COMMON_KBZFCX\">"+
	"<NSRSBH>%s</NSRSBH>"+
	"<FPLXDM>%s</FPLXDM>"+
	"<FPDM>%s</FPDM>"+
	"<FPHM>%s</FPHM>"+
	"</REQUEST_COMMON_KBZFCX>",
	xsfNsrsbh, fplxdm, fpdm, fphm)*/

	//实票作废
	return fmt.Sprintf("<REQUEST_COMMON_YKFPZF class=\"REQUEST_COMMON_YKFPZF\">"+
		"<NSRSBH>%s</NSRSBH>"+
		"<SKPBH>%s</SKPBH>"+
		"<FPLXDM>%s</FPLXDM>"+
		"<ZFLX>3</ZFLX>"+
		"<FPDM>%s</FPDM>"+
		"<FPHM>%s</FPHM>"+
		"<HJJE>%.2f</HJJE>"+
		"<ZFR>胡管玥</ZFR>"+
		"</REQUEST_COMMON_YKFPZF>",
		xsfNsrsbh, skpbh, fplxdm, fpdm, fphm, hjje)
}

func GetQueryContent(xsfNsrsbh string, orderSn string) string {
	return fmt.Sprintf("<REQUEST_COMMON_FPCX class=\"REQUEST_COMMON_FPCX\">"+
		"<FPQQLSH>%s</FPQQLSH>"+
		"<XSF_NSRSBH>%s</XSF_NSRSBH>"+
		"</REQUEST_COMMON_FPCX>",
		orderSn, xsfNsrsbh)
}

// Abandonment 发票作废 只支持单张 需要同时开票多张可用协程处理
func (c *Client) Abandonment(hjje float64, fplxdm string, fpdm string, fphm string) (*AbandonmentResult, error) {
	//组装 content xml数据
	content := GetAbandonmentContent(c.Config.XsfNsrsbh, c.GetPh(fplxdm), fplxdm, fpdm, fphm, hjje)

	//发送请求
	result, err := c.SoapRequest([]byte(content), c.Config.AbandonmentInterfaceCode)
	if err != nil {
		return nil, err
	}

	//解析返回数据
	doc := etree.NewDocument()
	err = doc.ReadFromString(result.Out)
	if err != nil {
		return nil, fmt.Errorf("发票通返回结构验证失败:%s", err.Error())
	}

	itfc := doc.SelectElement("interface")
	resultInfo := itfc.SelectElement("returnStateInfo")

	//验证是否成功
	if resultInfo.SelectElement("returnCode").Text() != "0000" {
		return nil, fmt.Errorf(resultInfo.SelectElement("returnMessage").Text())
	}

	//解析响应数据
	abandonmentResult := new(AbandonmentResult)
	respContent := itfc.SelectElement("Data").SelectElement("content").Text()
	respContentBase64, err := base64.StdEncoding.DecodeString(respContent)
	if err != nil {
		logx.Error(fmt.Sprintf("作废成功 但base64 content失败 err: %s", err.Error()))
	} else {
		err = xml.Unmarshal(respContentBase64, abandonmentResult)
		if err != nil {
			logx.Error(fmt.Sprintf("作废成功 但解析返回content失败 err: %s", err.Error()))
		}
	}

	return abandonmentResult, nil
}

func (c *Client) Query(orderSn string) (*QueryResult, error) {
	//组装 content xml数据
	content := GetQueryContent(c.Config.XsfNsrsbh, orderSn)

	//发送请求
	result, err := c.SoapRequest([]byte(content), c.Config.QueryInterfaceCode)
	if err != nil {
		return nil, err
	}

	//解析返回数据
	doc := etree.NewDocument()
	err = doc.ReadFromString(result.Out)
	if err != nil {
		return nil, fmt.Errorf("发票通返回结构验证失败:%s", err.Error())
	}

	itfc := doc.SelectElement("interface")
	resultInfo := itfc.SelectElement("returnStateInfo")

	//验证是否成功
	if resultInfo.SelectElement("returnCode").Text() != "0000" {
		return nil, fmt.Errorf(resultInfo.SelectElement("returnMessage").Text())
	}

	//解析响应数据
	queryResult := new(QueryResult)
	respContent := itfc.SelectElement("Data").SelectElement("content").Text()
	respContentBase64, err := base64.StdEncoding.DecodeString(respContent)
	if err != nil {
		logx.Error(fmt.Sprintf("查询成功 但base64 content失败 err: %s", err.Error()))
	} else {
		fmt.Println(string(respContentBase64))
		err = xml.Unmarshal(respContentBase64, queryResult)
		if err != nil {
			logx.Error(fmt.Sprintf("查询成功 但解析返回content失败 err: %s", err.Error()))
		}
	}

	return queryResult, nil
}

// EcbEncrypt aes加密
func EcbEncrypt(data, key []byte) []byte {
	block, _ := aes.NewCipher(key)
	data = PKCS5Padding(data, block.BlockSize())
	decrypted := make([]byte, len(data))
	size := block.BlockSize()

	for bs, be := 0, size; bs < len(data); bs, be = bs+size, be+size {
		block.Encrypt(decrypted[bs:be], data[bs:be])
	}

	return decrypted
}
func PKCS5Padding(ciphertext []byte, blockSize int) []byte {
	padding := blockSize - len(ciphertext)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padtext...)
}
