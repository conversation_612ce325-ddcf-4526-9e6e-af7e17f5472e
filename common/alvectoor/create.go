package alvectoor

import (
	"errors"
	"fmt"
)

type SaveResp struct {
	ErrorResp
	Output []SaveInfo
}

type SaveInfo struct {
	Id      string `json:"id"`
	DocOp   string `json:"doc_op"`
	Code    int64  `json:"code"`
	Message string `json:"message"`
}

func (c *Client) Save(body map[string]interface{}) (*SaveResp, error) {
	r, err := c.httpUtil.R().
		SetHeader("dashvector-auth-token", c.<PERSON><PERSON>).
		SetBody(body).
		Post(fmt.Sprintf("https://%s/v1/collections/%s/docs", c.Endpoint, c.CollectionName))
	if err != nil {
		return nil, errors.New(fmt.Sprintf("保存失败：%s", err))
	}

	var result SaveResp
	errResult := c.Unmarshal(r.Body(), &result)
	if errResult != nil {
		c.logger.Error("Save err：", r.String())
		return nil, errors.New(errResult.Resp.ErrorMsg)
	}

	return &result, nil
}
