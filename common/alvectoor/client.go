package alvectoor

import (
	"context"
	"encoding/json"
	"github.com/go-resty/resty/v2"
	"github.com/zeromicro/go-zero/core/logx"
	"time"
)

type ErrorResp struct {
	Resp ErrorInfo `json:"error_response"`
}
type ErrorInfo struct {
	ErrorMsg  string `json:"message"`
	ErrorCode int64  `json:"code"`
	RequestId string `json:"request_id"`
}

type Client struct {
	Endpoint       string
	ApiKey         string
	CollectionName string
	httpUtil       *resty.Client
	logger         logx.Logger
}

func NewClient(endpoint, apiKey, collectionName string) *Client {
	return &Client{
		Endpoint:       endpoint,
		ApiKey:         apiKey,
		CollectionName: collectionName,
		httpUtil:       resty.New().SetTimeout(3 * time.Second),
		logger:         logx.WithContext(context.Background()),
	}
}

func (c *Client) Unmarshal(body []byte, result interface{}) *ErrorResp {
	var e ErrorResp
	err := json.Unmarshal(body, &e)
	if err != nil {
		return &ErrorResp{Resp: ErrorInfo{
			ErrorMsg:  err.Error(),
			ErrorCode: -1,
		}}
	}
	if e.Resp.ErrorCode != 0 {
		return &e
	}

	err = json.Unmarshal(body, &result)
	if err != nil {
		return &ErrorResp{Resp: ErrorInfo{
			ErrorMsg:  err.Error(),
			ErrorCode: -1,
		}}
	}
	return nil
}
