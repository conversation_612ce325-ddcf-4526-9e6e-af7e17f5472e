package alvectoor

import (
	"errors"
	"fmt"
)

type QueryResp struct {
	ErrorResp
	Output []QueryInfo
}

type QueryInfo struct {
	Id     string                 `json:"id"`
	Vector []float64              `json:"vector"`
	Fields map[string]interface{} `json:"fields"`
	Score  float64                `json:"score"`
}

func (c *Client) Query(body map[string]interface{}) (*QueryResp, error) {
	r, err := c.httpUtil.R().
		SetHeader("dashvector-auth-token", c.<PERSON><PERSON>).
		SetBody(body).
		Post(fmt.Sprintf("https://%s/v1/collections/%s/query", c.Endpoint, c.CollectionName))
	if err != nil {
		return nil, errors.New(fmt.Sprintf("检索失败：%s", err))
	}

	var result QueryResp
	errResult := c.Unmarshal(r.Body(), &result)
	if errResult != nil {
		c.logger.Error("Query err：", r.String())
		return nil, errors.New(errResult.Resp.ErrorMsg)
	}

	return &result, nil
}
