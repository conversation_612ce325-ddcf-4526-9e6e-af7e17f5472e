package alvectoor

import (
	"errors"
	"fmt"
)

type DeleteResp struct {
	ErrorResp
	Output []DeleteInfo
}

type DeleteInfo struct {
	Id      string `json:"id"`
	DocOp   string `json:"doc_op"`
	Code    int64  `json:"code"`
	Message string `json:"message"`
}

func (c *Client) Delete(body map[string]interface{}) (*DeleteResp, error) {
	r, err := c.httpUtil.R().
		SetHeader("dashvector-auth-token", c.<PERSON><PERSON>).
		SetBody(body).
		Delete(fmt.Sprintf("https://%s/v1/collections/%s/docs", c.Endpoint, c.CollectionName))
	if err != nil {
		return nil, errors.New(fmt.Sprintf("删除失败：%s", err))
	}

	var result DeleteResp
	errResult := c.Unmarshal(r.Body(), &result)
	if errResult != nil {
		c.logger.Error("Delete err：", r.String())
		return nil, errors.New(errResult.Resp.ErrorMsg)
	}

	return &result, nil
}
