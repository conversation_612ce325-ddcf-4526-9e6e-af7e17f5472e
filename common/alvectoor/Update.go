package alvectoor

import (
	"errors"
	"fmt"
)

func (c *Client) Update(body map[string]interface{}) (*SaveResp, error) {
	r, err := c.httpUtil.R().
		SetHeader("dashvector-auth-token", c<PERSON><PERSON><PERSON>).
		SetBody(body).
		Put(fmt.Sprintf("https://%s/v1/collections/%s/docs", c.Endpoint, c.CollectionName))
	if err != nil {
		return nil, errors.New(fmt.Sprintf("保存失败：%s", err))
	}

	var result SaveResp
	errResult := c.Unmarshal(r.Body(), &result)
	if errResult != nil {
		c.logger.Error("Update err：", r.String())
		return nil, errors.New(errResult.Resp.ErrorMsg)
	}

	return &result, nil
}
